package middleware

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"quester-backend/internal/common/redis"

	"github.com/gofiber/fiber/v3"
)

// ErrorHandler handles application errors
func ErrorHandler(c fiber.Ctx, err error) error {
	// Default error
	code := fiber.StatusInternalServerError
	message := "Internal Server Error"

	// Check if it's a Fiber error
	if e, ok := err.(*fiber.Error); ok {
		code = e.Code
		message = e.Message
	}

	// Log error for debugging
	if code >= 500 {
		fmt.Printf("Error: %v\n", err)
	}

	// Return error response
	return c.Status(code).JSON(fiber.Map{
		"error":     message,
		"timestamp": time.Now().UTC(),
		"path":      c.Path(),
	})
}

// SecurityHeaders adds security headers to responses
func SecurityHeaders() fiber.Handler {
	return func(c fiber.Ctx) error {
		// Security headers
		c.Set("X-Content-Type-Options", "nosniff")
		c.Set("X-Frame-Options", "DENY")
		c.Set("X-XSS-Protection", "1; mode=block")
		c.Set("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Set("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

		// Remove server header
		c.Set("Server", "")

		return c.Next()
	}
}

// RateLimit implements rate limiting using Redis
func RateLimit(redisClient *redis.Client) fiber.Handler {
	return func(c fiber.Ctx) error {
		// Get client IP
		ip := c.IP()

		// Create rate limit key
		key := fmt.Sprintf("rate_limit:%s", ip)

		// Get current count
		ctx := context.Background()
		current, err := redisClient.Get(ctx, key).Int()
		if err != nil && err.Error() != "redis: nil" {
			// If Redis is down, allow the request
			return c.Next()
		}

		// Rate limit: 100 requests per minute
		limit := 100
		window := time.Minute

		if current >= limit {
			return c.Status(fiber.StatusTooManyRequests).JSON(fiber.Map{
				"error":       "Rate limit exceeded",
				"retry_after": window.Seconds(),
			})
		}

		// Increment counter
		pipe := redisClient.Pipeline()
		pipe.Incr(ctx, key)
		pipe.Expire(ctx, key, window)
		_, err = pipe.Exec(ctx)
		if err != nil {
			// If Redis is down, allow the request
			return c.Next()
		}

		// Add rate limit headers
		c.Set("X-RateLimit-Limit", strconv.Itoa(limit))
		c.Set("X-RateLimit-Remaining", strconv.Itoa(limit-current-1))
		c.Set("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(window).Unix(), 10))

		return c.Next()
	}
}

// RequestLogger logs incoming requests
func RequestLogger() fiber.Handler {
	return func(c fiber.Ctx) error {
		start := time.Now()

		// Process request
		err := c.Next()

		// Log request
		duration := time.Since(start)
		fmt.Printf("[%s] %s %s - %d - %v\n",
			start.Format("2006-01-02 15:04:05"),
			c.Method(),
			c.Path(),
			c.Response().StatusCode(),
			duration,
		)

		return err
	}
}

// ValidateJSON validates JSON request body
func ValidateJSON() fiber.Handler {
	return func(c fiber.Ctx) error {
		if c.Method() == "POST" || c.Method() == "PUT" || c.Method() == "PATCH" {
			contentType := c.Get("Content-Type")
			if contentType != "" && contentType != "application/json" {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
					"error": "Content-Type must be application/json",
				})
			}
		}

		return c.Next()
	}
}

// Pagination middleware to handle pagination parameters
func Pagination() fiber.Handler {
	return func(c fiber.Ctx) error {
		// Get pagination parameters
		pageStr := c.Query("page", "1")
		limitStr := c.Query("limit", "20")

		page, err := strconv.Atoi(pageStr)
		if err != nil {
			page = 1
		}

		limit, err := strconv.Atoi(limitStr)
		if err != nil {
			limit = 20
		}

		// Validate parameters
		if page < 1 {
			page = 1
		}
		if limit < 1 || limit > 100 {
			limit = 20
		}

		// Calculate offset
		offset := (page - 1) * limit

		// Store in locals
		c.Locals("page", page)
		c.Locals("limit", limit)
		c.Locals("offset", offset)

		return c.Next()
	}
}

// GetPagination extracts pagination parameters from context
func GetPagination(c fiber.Ctx) (page, limit, offset int) {
	page = c.Locals("page").(int)
	limit = c.Locals("limit").(int)
	offset = c.Locals("offset").(int)
	return
}

// CORS middleware for development
func DevCORS() fiber.Handler {
	return func(c fiber.Ctx) error {
		c.Set("Access-Control-Allow-Origin", "*")
		c.Set("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
		c.Set("Access-Control-Allow-Headers", "Origin,Content-Type,Accept,Authorization")
		c.Set("Access-Control-Allow-Credentials", "true")

		if c.Method() == "OPTIONS" {
			return c.SendStatus(fiber.StatusNoContent)
		}

		return c.Next()
	}
}
