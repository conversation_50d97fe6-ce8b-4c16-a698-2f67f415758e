package user

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents the main user entity
type User struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email     string    `json:"email" gorm:"uniqueIndex;not null" validate:"required,email"`
	Username  string    `json:"username" gorm:"uniqueIndex;not null" validate:"required,min=3,max=50"`
	Password  string    `json:"-" gorm:"not null" validate:"required,min=6"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	IsVerified bool     `json:"is_verified" gorm:"default:false"`
	Role      string    `json:"role" gorm:"default:'user'" validate:"oneof=user admin moderator"`
	
	// Relationships
	Profile     UserProfile     `json:"profile,omitempty" gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE"`
	Statistics  UserStatistics  `json:"statistics,omitempty" gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE"`
	Activities  []UserActivity  `json:"activities,omitempty" gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// UserProfile contains extended user information
type UserProfile struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	FirstName   string    `json:"first_name" validate:"max=50"`
	LastName    string    `json:"last_name" validate:"max=50"`
	DisplayName string    `json:"display_name" validate:"max=100"`
	Avatar      string    `json:"avatar"`
	Bio         string    `json:"bio" validate:"max=500"`
	Location    string    `json:"location" validate:"max=100"`
	Website     string    `json:"website" validate:"url"`
	DateOfBirth *time.Time `json:"date_of_birth"`
	
	// Gamification
	Level       int     `json:"level" gorm:"default:1"`
	Experience  int     `json:"experience" gorm:"default:0"`
	Coins       int     `json:"coins" gorm:"default:100"`
	Gems        int     `json:"gems" gorm:"default:0"`
	
	// Preferences
	Theme           string `json:"theme" gorm:"default:'light'" validate:"oneof=light dark auto"`
	Language        string `json:"language" gorm:"default:'en'" validate:"len=2"`
	Timezone        string `json:"timezone" gorm:"default:'UTC'"`
	EmailNotifications bool `json:"email_notifications" gorm:"default:true"`
	PushNotifications  bool `json:"push_notifications" gorm:"default:true"`
	
	// Privacy
	ProfileVisibility string `json:"profile_visibility" gorm:"default:'public'" validate:"oneof=public friends private"`
	ShowOnLeaderboard bool   `json:"show_on_leaderboard" gorm:"default:true"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// UserStatistics tracks user performance metrics
type UserStatistics struct {
	ID     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null;uniqueIndex"`
	
	// Quest Statistics
	QuestsCompleted    int `json:"quests_completed" gorm:"default:0"`
	QuestsInProgress   int `json:"quests_in_progress" gorm:"default:0"`
	QuestsFailed       int `json:"quests_failed" gorm:"default:0"`
	DailyQuestsStreak  int `json:"daily_quests_streak" gorm:"default:0"`
	WeeklyQuestsStreak int `json:"weekly_quests_streak" gorm:"default:0"`
	
	// Achievement Statistics
	AchievementsUnlocked int `json:"achievements_unlocked" gorm:"default:0"`
	BadgesEarned         int `json:"badges_earned" gorm:"default:0"`
	
	// Social Statistics
	FriendsCount    int `json:"friends_count" gorm:"default:0"`
	FollowersCount  int `json:"followers_count" gorm:"default:0"`
	FollowingCount  int `json:"following_count" gorm:"default:0"`
	
	// Activity Statistics
	TotalLoginDays     int       `json:"total_login_days" gorm:"default:0"`
	CurrentLoginStreak int       `json:"current_login_streak" gorm:"default:0"`
	LastLoginDate      *time.Time `json:"last_login_date"`
	TotalTimeSpent     int       `json:"total_time_spent" gorm:"default:0"` // in minutes
	
	// Marketplace Statistics
	ItemsPurchased int `json:"items_purchased" gorm:"default:0"`
	ItemsSold      int `json:"items_sold" gorm:"default:0"`
	TotalSpent     int `json:"total_spent" gorm:"default:0"`
	TotalEarned    int `json:"total_earned" gorm:"default:0"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// UserActivity tracks user actions and events
type UserActivity struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Type        string    `json:"type" gorm:"not null" validate:"required"`
	Description string    `json:"description" gorm:"not null"`
	Metadata    string    `json:"metadata" gorm:"type:jsonb"` // JSON data for additional context
	Points      int       `json:"points" gorm:"default:0"`
	
	// Related entities
	QuestID       *uuid.UUID `json:"quest_id,omitempty" gorm:"type:uuid"`
	AchievementID *uuid.UUID `json:"achievement_id,omitempty" gorm:"type:uuid"`
	ItemID        *uuid.UUID `json:"item_id,omitempty" gorm:"type:uuid"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// FriendRelationship represents friendship between users
type FriendRelationship struct {
	ID         uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID     uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	FriendID   uuid.UUID `json:"friend_id" gorm:"type:uuid;not null"`
	Status     string    `json:"status" gorm:"default:'pending'" validate:"oneof=pending accepted blocked"`
	RequestedBy uuid.UUID `json:"requested_by" gorm:"type:uuid;not null"`
	
	// Relationships
	User   User `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Friend User `json:"friend,omitempty" gorm:"foreignKey:FriendID"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// WalletTransaction tracks virtual currency transactions
type WalletTransaction struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Type        string    `json:"type" gorm:"not null" validate:"oneof=earn spend transfer refund"`
	Currency    string    `json:"currency" gorm:"not null" validate:"oneof=coins gems"`
	Amount      int       `json:"amount" gorm:"not null"`
	Balance     int       `json:"balance" gorm:"not null"`
	Description string    `json:"description" gorm:"not null"`
	Reference   string    `json:"reference"` // Reference to related entity (quest_id, item_id, etc.)
	Metadata    string    `json:"metadata" gorm:"type:jsonb"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Activity types constants
const (
	ActivityTypeLogin           = "login"
	ActivityTypeQuestStart      = "quest_start"
	ActivityTypeQuestComplete   = "quest_complete"
	ActivityTypeQuestFail       = "quest_fail"
	ActivityTypeAchievementUnlock = "achievement_unlock"
	ActivityTypeItemPurchase    = "item_purchase"
	ActivityTypeFriendAdd       = "friend_add"
	ActivityTypeLevelUp         = "level_up"
	ActivityTypeProfileUpdate   = "profile_update"
)

// Friend status constants
const (
	FriendStatusPending  = "pending"
	FriendStatusAccepted = "accepted"
	FriendStatusBlocked  = "blocked"
)

// User roles constants
const (
	RoleUser      = "user"
	RoleAdmin     = "admin"
	RoleModerator = "moderator"
)
