package user

import (
	"strconv"

	"quester-backend/internal/middleware"

	"github.com/gofiber/fiber/v3"
	"github.com/google/uuid"
)

type Handler struct {
	service Service
}

func NewHandler(service Service) *Handler {
	return &Handler{service: service}
}

// @Summary Register a new user
// @Description Register a new user account
// @Tags auth
// @Accept json
// @Produce json
// @Param request body RegisterRequest true "Registration data"
// @Success 201 {object} AuthResponse
// @Failure 400 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Router /auth/register [post]
func (h *Handler) Register(c fiber.Ctx) error {
	var req RegisterRequest
	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"invalid_request", "Invalid request body", c.<PERSON>()))
	}

	response, err := h.service.Register(req)
	if err != nil {
		return c.Status(fiber.StatusConflict).JSON(NewErrorResponse(
			"registration_failed", err.Error(), c.Path()))
	}

	return c.Status(fiber.StatusCreated).JSON(response)
}

// @Summary Login user
// @Description Authenticate user and return tokens
// @Tags auth
// @Accept json
// @Produce json
// @Param request body LoginRequest true "Login credentials"
// @Success 200 {object} AuthResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/login [post]
func (h *Handler) Login(c fiber.Ctx) error {
	var req LoginRequest
	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	response, err := h.service.Login(req)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"login_failed", err.Error(), c.Path()))
	}

	return c.JSON(response)
}

// @Summary Refresh access token
// @Description Get new access token using refresh token
// @Tags auth
// @Accept json
// @Produce json
// @Param refresh_token body string true "Refresh token"
// @Success 200 {object} AuthResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/refresh [post]
func (h *Handler) RefreshToken(c fiber.Ctx) error {
	var req struct {
		RefreshToken string `json:"refresh_token"`
	}
	
	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	response, err := h.service.RefreshToken(req.RefreshToken)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"refresh_failed", err.Error(), c.Path()))
	}

	return c.JSON(response)
}

// @Summary Logout user
// @Description Logout user and invalidate tokens
// @Tags auth
// @Security BearerAuth
// @Success 200 {object} SuccessResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/logout [post]
func (h *Handler) Logout(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	if err := h.service.Logout(userID); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(NewErrorResponse(
			"logout_failed", err.Error(), c.Path()))
	}

	return c.JSON(NewSuccessResponse("Logged out successfully", nil))
}

// @Summary Get user profile
// @Description Get current user's profile information
// @Tags users
// @Security BearerAuth
// @Produce json
// @Success 200 {object} ProfileResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/profile [get]
func (h *Handler) GetProfile(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	user, err := h.service.GetUserByID(userID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(NewErrorResponse(
			"user_not_found", err.Error(), c.Path()))
	}

	stats, err := h.service.GetStatistics(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(NewErrorResponse(
			"stats_error", err.Error(), c.Path()))
	}

	response := NewProfileResponse(user.Profile, *stats)
	return c.JSON(response)
}

// @Summary Update user profile
// @Description Update current user's profile information
// @Tags users
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body UpdateProfileRequest true "Profile update data"
// @Success 200 {object} UserProfile
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/profile [put]
func (h *Handler) UpdateProfile(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	var req UpdateProfileRequest
	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	profile, err := h.service.UpdateProfile(userID, req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(NewErrorResponse(
			"update_failed", err.Error(), c.Path()))
	}

	return c.JSON(profile)
}

// @Summary Get user statistics
// @Description Get current user's statistics
// @Tags users
// @Security BearerAuth
// @Produce json
// @Success 200 {object} UserStatistics
// @Failure 401 {object} ErrorResponse
// @Router /users/statistics [get]
func (h *Handler) GetStatistics(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	stats, err := h.service.GetStatistics(userID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(NewErrorResponse(
			"stats_not_found", err.Error(), c.Path()))
	}

	return c.JSON(stats)
}

// @Summary Get user activities
// @Description Get current user's activity history
// @Tags users
// @Security BearerAuth
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} PaginatedResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/activities [get]
func (h *Handler) GetActivities(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "20"))
	offset := (page - 1) * limit

	activities, err := h.service.GetActivities(userID, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(NewErrorResponse(
			"activities_error", err.Error(), c.Path()))
	}

	// Convert to response format
	activityResponses := make([]ActivityResponse, len(activities))
	for i, activity := range activities {
		activityResponses[i] = ActivityResponse{
			ID:          activity.ID,
			Type:        activity.Type,
			Description: activity.Description,
			Points:      activity.Points,
			QuestID:     activity.QuestID,
			CreatedAt:   activity.CreatedAt,
		}
	}

	response := NewPaginatedResponse(activityResponses, page, limit, len(activities))
	return c.JSON(response)
}

// @Summary Get leaderboard
// @Description Get leaderboard rankings
// @Tags users
// @Security BearerAuth
// @Produce json
// @Param type query string false "Leaderboard type" Enums(experience, quests) default(experience)
// @Param limit query int false "Number of entries" default(50)
// @Success 200 {object} LeaderboardResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/leaderboard [get]
func (h *Handler) GetLeaderboard(c fiber.Ctx) error {
	leaderboardType := c.Query("type", "experience")
	limit, _ := strconv.Atoi(c.Query("limit", "50"))

	entries, err := h.service.GetLeaderboard(leaderboardType, limit)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(NewErrorResponse(
			"leaderboard_error", err.Error(), c.Path()))
	}

	response := LeaderboardResponse{
		Type:    leaderboardType,
		Entries: entries,
	}

	return c.JSON(response)
}

// @Summary Get friends
// @Description Get current user's friends list
// @Tags users
// @Security BearerAuth
// @Produce json
// @Success 200 {array} FriendResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/friends [get]
func (h *Handler) GetFriends(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	friends, err := h.service.GetFriends(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(NewErrorResponse(
			"friends_error", err.Error(), c.Path()))
	}

	// Convert to response format
	friendResponses := make([]FriendResponse, len(friends))
	for i, friend := range friends {
		friendResponses[i] = FriendResponse{
			ID:          friend.ID,
			Username:    friend.Username,
			DisplayName: friend.Profile.DisplayName,
			Avatar:      friend.Profile.Avatar,
			Level:       friend.Profile.Level,
			IsOnline:    false, // TODO: Implement online status
			LastSeen:    friend.UpdatedAt,
		}
	}

	return c.JSON(friendResponses)
}

// @Summary Add friend
// @Description Send friend request to another user
// @Tags users
// @Security BearerAuth
// @Param id path string true "Friend User ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/friends/{id} [post]
func (h *Handler) AddFriend(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	friendID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"invalid_id", "Invalid friend ID", c.Path()))
	}

	if err := h.service.AddFriend(userID, friendID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"add_friend_failed", err.Error(), c.Path()))
	}

	return c.JSON(NewSuccessResponse("Friend request sent", nil))
}

// @Summary Update friendship
// @Description Accept, reject, or block a friend request
// @Tags users
// @Security BearerAuth
// @Param id path string true "Friendship ID"
// @Param request body UpdateFriendshipRequest true "Friendship status"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/friends/{id} [put]
func (h *Handler) UpdateFriendship(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	friendshipID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"invalid_id", "Invalid friendship ID", c.Path()))
	}

	var req UpdateFriendshipRequest
	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	if err := h.service.UpdateFriendship(userID, friendshipID, req.Status); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"update_friendship_failed", err.Error(), c.Path()))
	}

	return c.JSON(NewSuccessResponse("Friendship updated", nil))
}

// @Summary Remove friend
// @Description Remove a friend or cancel friend request
// @Tags users
// @Security BearerAuth
// @Param id path string true "Friend User ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/friends/{id} [delete]
func (h *Handler) RemoveFriend(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	friendID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"invalid_id", "Invalid friend ID", c.Path()))
	}

	if err := h.service.RemoveFriend(userID, friendID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(
			"remove_friend_failed", err.Error(), c.Path()))
	}

	return c.JSON(NewSuccessResponse("Friend removed", nil))
}

// @Summary Get wallet
// @Description Get current user's wallet balance
// @Tags users
// @Security BearerAuth
// @Produce json
// @Success 200 {object} WalletResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/wallet [get]
func (h *Handler) GetWallet(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	wallet, err := h.service.GetWallet(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(NewErrorResponse(
			"wallet_error", err.Error(), c.Path()))
	}

	return c.JSON(wallet)
}

// @Summary Get wallet transactions
// @Description Get current user's wallet transaction history
// @Tags users
// @Security BearerAuth
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} PaginatedResponse
// @Failure 401 {object} ErrorResponse
// @Router /users/wallet/transactions [get]
func (h *Handler) GetWalletTransactions(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "20"))
	offset := (page - 1) * limit

	transactions, err := h.service.GetWalletTransactions(userID, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(NewErrorResponse(
			"transactions_error", err.Error(), c.Path()))
	}

	response := NewPaginatedResponse(transactions, page, limit, len(transactions))
	return c.JSON(response)
}

// Admin endpoints
func (h *Handler) GetAllUsers(c fiber.Ctx) error {
	// TODO: Implement admin user listing
	return c.JSON(NewSuccessResponse("Admin endpoint - not implemented", nil))
}

// Placeholder methods for missing endpoints
func (h *Handler) ForgotPassword(c fiber.Ctx) error {
	return c.JSON(NewSuccessResponse("Forgot password - not implemented", nil))
}

func (h *Handler) ResetPassword(c fiber.Ctx) error {
	return c.JSON(NewSuccessResponse("Reset password - not implemented", nil))
}
