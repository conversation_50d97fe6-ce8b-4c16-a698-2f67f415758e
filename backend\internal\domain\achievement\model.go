package achievement

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Achievement represents an achievement that users can unlock
type Achievement struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Title       string    `json:"title" gorm:"not null" validate:"required,min=3,max=200"`
	Description string    `json:"description" gorm:"not null" validate:"required,max=1000"`
	ShortDesc   string    `json:"short_desc" validate:"max=200"`
	
	// Achievement properties
	CategoryID uuid.UUID `json:"category_id" gorm:"type:uuid;not null"`
	Type       string    `json:"type" gorm:"not null" validate:"oneof=progress milestone collection social time_based special"`
	Rarity     string    `json:"rarity" gorm:"default:'common'" validate:"oneof=common uncommon rare epic legendary"`
	Points     int       `json:"points" gorm:"default:0"`
	
	// Requirements
	RequirementType  string `json:"requirement_type" gorm:"not null" validate:"oneof=quest_count achievement_count login_streak level experience friends social_action custom"`
	RequirementValue int    `json:"requirement_value" gorm:"default:1"`
	RequirementData  string `json:"requirement_data" gorm:"type:jsonb"` // JSON data for complex requirements
	
	// Rewards
	ExperienceReward int `json:"experience_reward" gorm:"default:0"`
	CoinsReward      int `json:"coins_reward" gorm:"default:0"`
	GemsReward       int `json:"gems_reward" gorm:"default:0"`
	
	// Display
	IconURL     string `json:"icon_url"`
	BadgeURL    string `json:"badge_url"`
	Color       string `json:"color" gorm:"default:'#007bff'" validate:"hexcolor"`
	IsSecret    bool   `json:"is_secret" gorm:"default:false"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`
	SortOrder   int    `json:"sort_order" gorm:"default:0"`
	
	// Relationships
	Category AchievementCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Rewards  []AchievementReward `json:"rewards,omitempty" gorm:"foreignKey:AchievementID;constraint:OnDelete:CASCADE"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// AchievementCategory represents achievement categories
type AchievementCategory struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null" validate:"required,min=2,max=50"`
	Description string    `json:"description" validate:"max=200"`
	Color       string    `json:"color" gorm:"default:'#007bff'" validate:"hexcolor"`
	IconURL     string    `json:"icon_url"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	SortOrder   int       `json:"sort_order" gorm:"default:0"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// AchievementReward represents additional rewards for achievement completion
type AchievementReward struct {
	ID            uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	AchievementID uuid.UUID `json:"achievement_id" gorm:"type:uuid;not null"`
	
	// Reward details
	Type        string     `json:"type" gorm:"not null" validate:"oneof=item badge title avatar_frame"`
	ItemID      *uuid.UUID `json:"item_id,omitempty" gorm:"type:uuid"`
	Quantity    int        `json:"quantity" gorm:"default:1"`
	Name        string     `json:"name" gorm:"not null"`
	Description string     `json:"description"`
	ImageURL    string     `json:"image_url"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// UserAchievement represents a user's unlocked achievements
type UserAchievement struct {
	ID            uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID        uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	AchievementID uuid.UUID `json:"achievement_id" gorm:"type:uuid;not null;index"`
	
	// Progress tracking
	Progress      int        `json:"progress" gorm:"default:0"`
	IsUnlocked    bool       `json:"is_unlocked" gorm:"default:false"`
	UnlockedAt    *time.Time `json:"unlocked_at"`
	
	// Rewards tracking
	RewardsClaimed bool       `json:"rewards_claimed" gorm:"default:false"`
	ClaimedAt      *time.Time `json:"claimed_at"`
	
	// Display tracking
	IsDisplayed   bool       `json:"is_displayed" gorm:"default:false"`
	DisplayedAt   *time.Time `json:"displayed_at"`
	IsFavorite    bool       `json:"is_favorite" gorm:"default:false"`
	
	// Relationships
	Achievement Achievement `json:"achievement,omitempty" gorm:"foreignKey:AchievementID"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Achievement type constants
const (
	AchievementTypeProgress   = "progress"
	AchievementTypeMilestone  = "milestone"
	AchievementTypeCollection = "collection"
	AchievementTypeSocial     = "social"
	AchievementTypeTimeBased  = "time_based"
	AchievementTypeSpecial    = "special"
)

// Achievement rarity constants
const (
	AchievementRarityCommon    = "common"
	AchievementRarityUncommon  = "uncommon"
	AchievementRarityRare      = "rare"
	AchievementRarityEpic      = "epic"
	AchievementRarityLegendary = "legendary"
)

// Achievement requirement type constants
const (
	RequirementTypeQuestCount       = "quest_count"
	RequirementTypeAchievementCount = "achievement_count"
	RequirementTypeLoginStreak      = "login_streak"
	RequirementTypeLevel            = "level"
	RequirementTypeExperience       = "experience"
	RequirementTypeFriends          = "friends"
	RequirementTypeSocialAction     = "social_action"
	RequirementTypeCustom           = "custom"
)

// Achievement reward type constants
const (
	AchievementRewardTypeItem        = "item"
	AchievementRewardTypeBadge       = "badge"
	AchievementRewardTypeTitle       = "title"
	AchievementRewardTypeAvatarFrame = "avatar_frame"
)
