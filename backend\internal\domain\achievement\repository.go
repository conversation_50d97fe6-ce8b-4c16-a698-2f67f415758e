package achievement

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	GetAchievements(limit, offset int) ([]Achievement, error)
	GetAchievementByID(id uuid.UUID) (*Achievement, error)
	GetCategories() ([]AchievementCategory, error)
	GetUserAchievements(userID uuid.UUID) ([]UserAchievement, error)
	CreateAchievement(achievement *Achievement) error
	UpdateAchievement(achievement *Achievement) error
	DeleteAchievement(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetAchievements(limit, offset int) ([]Achievement, error) {
	var achievements []Achievement
	err := r.db.Preload("Category").Limit(limit).Offset(offset).Find(&achievements).Error
	return achievements, err
}

func (r *repository) GetAchievementByID(id uuid.UUID) (*Achievement, error) {
	var achievement Achievement
	err := r.db.Preload("Category").Preload("Rewards").First(&achievement, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &achievement, nil
}

func (r *repository) GetCategories() ([]AchievementCategory, error) {
	var categories []AchievementCategory
	err := r.db.Where("is_active = ?", true).Order("sort_order").Find(&categories).Error
	return categories, err
}

func (r *repository) GetUserAchievements(userID uuid.UUID) ([]UserAchievement, error) {
	var userAchievements []UserAchievement
	err := r.db.Preload("Achievement").Where("user_id = ?", userID).Find(&userAchievements).Error
	return userAchievements, err
}

func (r *repository) CreateAchievement(achievement *Achievement) error {
	return r.db.Create(achievement).Error
}

func (r *repository) UpdateAchievement(achievement *Achievement) error {
	return r.db.Save(achievement).Error
}

func (r *repository) DeleteAchievement(id uuid.UUID) error {
	return r.db.Delete(&Achievement{}, "id = ?", id).Error
}
