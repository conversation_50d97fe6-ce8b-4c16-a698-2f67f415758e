package user

import (
	"time"

	"github.com/google/uuid"
)

// Request DTOs
type RegisterRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Username string `json:"username" validate:"required,min=3,max=50"`
	Password string `json:"password" validate:"required,min=6"`
}

type LoginRequest struct {
	Email    string `json:"email,omitempty" validate:"omitempty,email"`
	Username string `json:"username,omitempty" validate:"omitempty,min=3,max=50"`
	Password string `json:"password" validate:"required"`
}

type UpdateProfileRequest struct {
	FirstName   *string    `json:"first_name,omitempty" validate:"omitempty,max=50"`
	LastName    *string    `json:"last_name,omitempty" validate:"omitempty,max=50"`
	DisplayName *string    `json:"display_name,omitempty" validate:"omitempty,max=100"`
	Bio         *string    `json:"bio,omitempty" validate:"omitempty,max=500"`
	Location    *string    `json:"location,omitempty" validate:"omitempty,max=100"`
	Website     *string    `json:"website,omitempty" validate:"omitempty,url"`
	Avatar      *string    `json:"avatar,omitempty"`
	Theme       *string    `json:"theme,omitempty" validate:"omitempty,oneof=light dark auto"`
	Language    *string    `json:"language,omitempty" validate:"omitempty,len=2"`
	Timezone    *string    `json:"timezone,omitempty"`
	DateOfBirth *time.Time `json:"date_of_birth,omitempty"`
}

type FriendRequestRequest struct {
	FriendID uuid.UUID `json:"friend_id" validate:"required"`
}

type UpdateFriendshipRequest struct {
	Status string `json:"status" validate:"required,oneof=accepted rejected blocked"`
}

// Response DTOs
type AuthResponse struct {
	User         User   `json:"user"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
}

type UserResponse struct {
	ID       uuid.UUID   `json:"id"`
	Email    string      `json:"email"`
	Username string      `json:"username"`
	IsActive bool        `json:"is_active"`
	Role     string      `json:"role"`
	Profile  UserProfile `json:"profile"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

type ProfileResponse struct {
	UserProfile
	Statistics UserStatistics `json:"statistics"`
}

type WalletResponse struct {
	Coins int `json:"coins"`
	Gems  int `json:"gems"`
}

type LeaderboardEntry struct {
	Rank        int       `json:"rank"`
	UserID      uuid.UUID `json:"user_id"`
	DisplayName string    `json:"display_name"`
	Avatar      string    `json:"avatar"`
	Score       int       `json:"score"`
	Level       int       `json:"level"`
}

type LeaderboardResponse struct {
	Type    string             `json:"type"`
	Entries []LeaderboardEntry `json:"entries"`
	UserRank *LeaderboardEntry `json:"user_rank,omitempty"`
}

type FriendResponse struct {
	ID          uuid.UUID `json:"id"`
	Username    string    `json:"username"`
	DisplayName string    `json:"display_name"`
	Avatar      string    `json:"avatar"`
	Level       int       `json:"level"`
	IsOnline    bool      `json:"is_online"`
	LastSeen    time.Time `json:"last_seen"`
}

type ActivityResponse struct {
	ID          uuid.UUID  `json:"id"`
	Type        string     `json:"type"`
	Description string     `json:"description"`
	Points      int        `json:"points"`
	QuestID     *uuid.UUID `json:"quest_id,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
}

// Pagination response wrapper
type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	Total      int         `json:"total"`
	TotalPages int         `json:"total_pages"`
	HasNext    bool        `json:"has_next"`
	HasPrev    bool        `json:"has_prev"`
}

// Error response
type ErrorResponse struct {
	Error     string    `json:"error"`
	Message   string    `json:"message,omitempty"`
	Timestamp time.Time `json:"timestamp"`
	Path      string    `json:"path,omitempty"`
}

// Success response
type SuccessResponse struct {
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// Helper functions for responses
func NewAuthResponse(user User, accessToken, refreshToken string, expiresIn int) *AuthResponse {
	return &AuthResponse{
		User:         user,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    expiresIn,
	}
}

func NewUserResponse(user User) *UserResponse {
	return &UserResponse{
		ID:        user.ID,
		Email:     user.Email,
		Username:  user.Username,
		IsActive:  user.IsActive,
		Role:      user.Role,
		Profile:   user.Profile,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}
}

func NewProfileResponse(profile UserProfile, stats UserStatistics) *ProfileResponse {
	return &ProfileResponse{
		UserProfile: profile,
		Statistics:  stats,
	}
}

func NewPaginatedResponse(data interface{}, page, limit, total int) *PaginatedResponse {
	totalPages := (total + limit - 1) / limit
	return &PaginatedResponse{
		Data:       data,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

func NewErrorResponse(err string, message string, path string) *ErrorResponse {
	return &ErrorResponse{
		Error:     err,
		Message:   message,
		Timestamp: time.Now().UTC(),
		Path:      path,
	}
}

func NewSuccessResponse(message string, data interface{}) *SuccessResponse {
	return &SuccessResponse{
		Message:   message,
		Data:      data,
		Timestamp: time.Now().UTC(),
	}
}
