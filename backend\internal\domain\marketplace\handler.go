package marketplace

import (
	"strconv"

	"quester-backend/internal/domain/user"
	"quester-backend/internal/middleware"

	"github.com/gofiber/fiber/v3"
	"github.com/google/uuid"
)

type Handler struct {
	service Service
}

func NewHandler(service Service) *Handler {
	return &Handler{service: service}
}

func (h *Handler) GetItems(c fiber.Ctx) error {
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "20"))
	offset := (page - 1) * limit

	items, err := h.service.GetItems(limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"items_error", err.Error(), c.Path()))
	}

	response := user.NewPaginatedResponse(items, page, limit, len(items))
	return c.<PERSON>(response)
}

func (h *Handler) GetItem(c fiber.Ctx) error {
	itemID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid item ID", c.Path()))
	}

	item, err := h.service.GetItem(itemID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(user.NewErrorResponse(
			"item_not_found", err.Error(), c.Path()))
	}

	return c.JSON(item)
}

func (h *Handler) GetCategories(c fiber.Ctx) error {
	categories, err := h.service.GetCategories()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"categories_error", err.Error(), c.Path()))
	}

	return c.JSON(categories)
}

func (h *Handler) GetCart(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	cart, err := h.service.GetCart(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"cart_error", err.Error(), c.Path()))
	}

	return c.JSON(cart)
}

func (h *Handler) AddToCart(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	var req struct {
		ItemID   uuid.UUID `json:"item_id"`
		Quantity int       `json:"quantity"`
	}

	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	if err := h.service.AddToCart(userID, req.ItemID, req.Quantity); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"add_to_cart_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Item added to cart", nil))
}

func (h *Handler) UpdateCartItem(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	itemID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid item ID", c.Path()))
	}

	var req struct {
		Quantity int `json:"quantity"`
	}

	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	if err := h.service.UpdateCartItem(userID, itemID, req.Quantity); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"update_cart_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Cart item updated", nil))
}

func (h *Handler) RemoveFromCart(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	itemID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid item ID", c.Path()))
	}

	if err := h.service.RemoveFromCart(userID, itemID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"remove_from_cart_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Item removed from cart", nil))
}

func (h *Handler) PurchaseItem(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	var req struct {
		ItemID   uuid.UUID `json:"item_id"`
		Quantity int       `json:"quantity"`
	}

	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	if err := h.service.PurchaseItem(userID, req.ItemID, req.Quantity); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"purchase_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Item purchased successfully", nil))
}

func (h *Handler) PurchaseCart(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	if err := h.service.PurchaseCart(userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"purchase_cart_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Cart purchased successfully", nil))
}

func (h *Handler) GetInventory(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	inventory, err := h.service.GetInventory(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"inventory_error", err.Error(), c.Path()))
	}

	return c.JSON(inventory)
}

func (h *Handler) EquipItem(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	itemID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid item ID", c.Path()))
	}

	if err := h.service.EquipItem(userID, itemID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"equip_item_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Item equipped successfully", nil))
}

func (h *Handler) CreateItem(c fiber.Ctx) error {
	var item Item
	if err := c.Bind().JSON(&item); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	if err := h.service.CreateItem(&item); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"create_item_failed", err.Error(), c.Path()))
	}

	return c.Status(fiber.StatusCreated).JSON(item)
}

func (h *Handler) UpdateItem(c fiber.Ctx) error {
	itemID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid item ID", c.Path()))
	}

	var item Item
	if err := c.Bind().JSON(&item); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	item.ID = itemID
	if err := h.service.UpdateItem(&item); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"update_item_failed", err.Error(), c.Path()))
	}

	return c.JSON(item)
}

func (h *Handler) DeleteItem(c fiber.Ctx) error {
	itemID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid item ID", c.Path()))
	}

	if err := h.service.DeleteItem(itemID); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"delete_item_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Item deleted successfully", nil))
}
