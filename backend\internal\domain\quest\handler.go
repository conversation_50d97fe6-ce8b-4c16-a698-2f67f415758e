package quest

import (
	"strconv"

	"quester-backend/internal/domain/user"
	"quester-backend/internal/middleware"

	"github.com/gofiber/fiber/v3"
	"github.com/google/uuid"
)

type Handler struct {
	service Service
}

func NewHandler(service Service) *Handler {
	return &Handler{service: service}
}

func (h *Handler) GetQuests(c fiber.Ctx) error {
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "20"))
	offset := (page - 1) * limit

	quests, err := h.service.GetQuests(limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"quests_error", err.Error(), c.Path()))
	}

	response := user.NewPaginatedResponse(quests, page, limit, len(quests))
	return c.<PERSON>(response)
}

func (h *Handler) GetQuest(c fiber.Ctx) error {
	questID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid quest ID", c.Path()))
	}

	quest, err := h.service.GetQuest(questID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(user.NewErrorResponse(
			"quest_not_found", err.Error(), c.Path()))
	}

	return c.JSON(quest)
}

func (h *Handler) GetCategories(c fiber.Ctx) error {
	categories, err := h.service.GetCategories()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"categories_error", err.Error(), c.Path()))
	}

	return c.JSON(categories)
}

func (h *Handler) GetRecommendedQuests(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	quests, err := h.service.GetRecommendedQuests(userID, limit)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"recommendations_error", err.Error(), c.Path()))
	}

	return c.JSON(quests)
}

func (h *Handler) StartQuest(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	questID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid quest ID", c.Path()))
	}

	if err := h.service.StartQuest(userID, questID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"start_quest_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Quest started successfully", nil))
}

func (h *Handler) UpdateProgress(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	questID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid quest ID", c.Path()))
	}

	var req struct {
		StepID   uuid.UUID `json:"step_id"`
		Progress int       `json:"progress"`
	}

	if err := c.Bind().JSON(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	if err := h.service.UpdateProgress(userID, questID, req.StepID, req.Progress); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"update_progress_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Progress updated successfully", nil))
}

func (h *Handler) CompleteQuest(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	questID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid quest ID", c.Path()))
	}

	if err := h.service.CompleteQuest(userID, questID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"complete_quest_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Quest completed successfully", nil))
}

func (h *Handler) ClaimReward(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	questID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid quest ID", c.Path()))
	}

	if err := h.service.ClaimReward(userID, questID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"claim_reward_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Reward claimed successfully", nil))
}

func (h *Handler) GetUserActiveQuests(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	quests, err := h.service.GetUserActiveQuests(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"active_quests_error", err.Error(), c.Path()))
	}

	return c.JSON(quests)
}

func (h *Handler) GetUserCompletedQuests(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "20"))
	offset := (page - 1) * limit

	quests, err := h.service.GetUserCompletedQuests(userID, limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"completed_quests_error", err.Error(), c.Path()))
	}

	response := user.NewPaginatedResponse(quests, page, limit, len(quests))
	return c.JSON(response)
}

// Admin endpoints
func (h *Handler) CreateQuest(c fiber.Ctx) error {
	var quest Quest
	if err := c.Bind().JSON(&quest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	if err := h.service.CreateQuest(&quest); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"create_quest_failed", err.Error(), c.Path()))
	}

	return c.Status(fiber.StatusCreated).JSON(quest)
}

func (h *Handler) UpdateQuest(c fiber.Ctx) error {
	questID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid quest ID", c.Path()))
	}

	var quest Quest
	if err := c.Bind().JSON(&quest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	quest.ID = questID
	if err := h.service.UpdateQuest(&quest); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"update_quest_failed", err.Error(), c.Path()))
	}

	return c.JSON(quest)
}

func (h *Handler) DeleteQuest(c fiber.Ctx) error {
	questID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid quest ID", c.Path()))
	}

	if err := h.service.DeleteQuest(questID); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"delete_quest_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Quest deleted successfully", nil))
}
