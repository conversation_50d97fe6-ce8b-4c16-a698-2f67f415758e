package achievement

import (
	"strconv"

	"quester-backend/internal/domain/user"
	"quester-backend/internal/middleware"

	"github.com/gofiber/fiber/v3"
	"github.com/google/uuid"
)

type Handler struct {
	service Service
}

func NewHandler(service Service) *Handler {
	return &Handler{service: service}
}

func (h *Handler) GetAchievements(c fiber.Ctx) error {
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "20"))
	offset := (page - 1) * limit

	achievements, err := h.service.GetAchievements(limit, offset)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"achievements_error", err.Error(), c.Path()))
	}

	response := user.NewPaginatedResponse(achievements, page, limit, len(achievements))
	return c.<PERSON>(response)
}

func (h *Handler) GetAchievement(c fiber.Ctx) error {
	achievementID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid achievement ID", c.Path()))
	}

	achievement, err := h.service.GetAchievement(achievementID)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(user.NewErrorResponse(
			"achievement_not_found", err.Error(), c.Path()))
	}

	return c.JSON(achievement)
}

func (h *Handler) GetCategories(c fiber.Ctx) error {
	categories, err := h.service.GetCategories()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"categories_error", err.Error(), c.Path()))
	}

	return c.JSON(categories)
}

func (h *Handler) GetUserAchievements(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	achievements, err := h.service.GetUserAchievements(userID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"user_achievements_error", err.Error(), c.Path()))
	}

	return c.JSON(achievements)
}

func (h *Handler) ClaimReward(c fiber.Ctx) error {
	userID, err := middleware.GetUserID(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(user.NewErrorResponse(
			"unauthorized", err.Error(), c.Path()))
	}

	achievementID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid achievement ID", c.Path()))
	}

	if err := h.service.ClaimReward(userID, achievementID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"claim_reward_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Achievement reward claimed successfully", nil))
}

func (h *Handler) CreateAchievement(c fiber.Ctx) error {
	var achievement Achievement
	if err := c.Bind().JSON(&achievement); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	if err := h.service.CreateAchievement(&achievement); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"create_achievement_failed", err.Error(), c.Path()))
	}

	return c.Status(fiber.StatusCreated).JSON(achievement)
}

func (h *Handler) UpdateAchievement(c fiber.Ctx) error {
	achievementID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid achievement ID", c.Path()))
	}

	var achievement Achievement
	if err := c.Bind().JSON(&achievement); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_request", "Invalid request body", c.Path()))
	}

	achievement.ID = achievementID
	if err := h.service.UpdateAchievement(&achievement); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"update_achievement_failed", err.Error(), c.Path()))
	}

	return c.JSON(achievement)
}

func (h *Handler) DeleteAchievement(c fiber.Ctx) error {
	achievementID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(user.NewErrorResponse(
			"invalid_id", "Invalid achievement ID", c.Path()))
	}

	if err := h.service.DeleteAchievement(achievementID); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(user.NewErrorResponse(
			"delete_achievement_failed", err.Error(), c.Path()))
	}

	return c.JSON(user.NewSuccessResponse("Achievement deleted successfully", nil))
}
