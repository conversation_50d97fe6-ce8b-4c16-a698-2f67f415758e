import 'package:flutter/material.dart';
import 'package:universal_platform/universal_platform.dart';
import 'breakpoints.dart';

/// Device type enumeration
enum DeviceType { mobile, tablet, desktop }

/// Platform type enumeration
enum PlatformType { android, ios, web, windows, macos, linux }

/// Screen size information
class ScreenInfo {
  final double width;
  final double height;
  final DeviceType deviceType;
  final PlatformType platformType;
  final bool isLandscape;
  final bool isPortrait;
  final double pixelRatio;
  final EdgeInsets padding;
  final EdgeInsets viewInsets;

  const ScreenInfo({
    required this.width,
    required this.height,
    required this.deviceType,
    required this.platformType,
    required this.isLandscape,
    required this.isPortrait,
    required this.pixelRatio,
    required this.padding,
    required this.viewInsets,
  });

  /// Create ScreenInfo from MediaQuery
  factory ScreenInfo.fromMediaQuery(MediaQueryData mediaQuery) {
    final size = mediaQuery.size;
    final width = size.width;
    final height = size.height;

    DeviceType deviceType;
    if (Breakpoints.isMobile(width)) {
      deviceType = DeviceType.mobile;
    } else if (Breakpoints.isTablet(width)) {
      deviceType = DeviceType.tablet;
    } else {
      deviceType = DeviceType.desktop;
    }

    PlatformType platformType;
    if (UniversalPlatform.isAndroid) {
      platformType = PlatformType.android;
    } else if (UniversalPlatform.isIOS) {
      platformType = PlatformType.ios;
    } else if (UniversalPlatform.isWeb) {
      platformType = PlatformType.web;
    } else if (UniversalPlatform.isWindows) {
      platformType = PlatformType.windows;
    } else if (UniversalPlatform.isMacOS) {
      platformType = PlatformType.macos;
    } else {
      platformType = PlatformType.linux;
    }

    return ScreenInfo(
      width: width,
      height: height,
      deviceType: deviceType,
      platformType: platformType,
      isLandscape: width > height,
      isPortrait: height > width,
      pixelRatio: mediaQuery.devicePixelRatio,
      padding: mediaQuery.padding,
      viewInsets: mediaQuery.viewInsets,
    );
  }

  /// Check if device is mobile
  bool get isMobile => deviceType == DeviceType.mobile;

  /// Check if device is tablet
  bool get isTablet => deviceType == DeviceType.tablet;

  /// Check if device is desktop
  bool get isDesktop => deviceType == DeviceType.desktop;

  /// Check if platform is Android
  bool get isAndroid => platformType == PlatformType.android;

  /// Check if platform is iOS
  bool get isIOS => platformType == PlatformType.ios;

  /// Check if platform is web
  bool get isWeb => platformType == PlatformType.web;

  /// Check if platform is mobile (Android or iOS)
  bool get isMobilePlatform => isAndroid || isIOS;

  /// Check if platform is desktop (Windows, macOS, Linux)
  bool get isDesktopPlatform => 
      platformType == PlatformType.windows ||
      platformType == PlatformType.macos ||
      platformType == PlatformType.linux;

  /// Get grid columns for current screen
  int get gridColumns => Breakpoints.getGridColumns(width);

  /// Get content padding for current screen
  double get contentPadding => Breakpoints.getContentPadding(width);

  /// Get max content width for current screen
  double get maxContentWidth => Breakpoints.getMaxContentWidth(width);
}

/// Responsive builder widget that provides screen information
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, ScreenInfo screenInfo) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenInfo = ScreenInfo.fromMediaQuery(mediaQuery);
    
    return builder(context, screenInfo);
  }
}

/// Responsive widget that builds different widgets based on screen size
class ResponsiveWidget extends StatelessWidget {
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? fallback;

  const ResponsiveWidget({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenInfo) {
        if (screenInfo.isMobile && mobile != null) {
          return mobile!;
        }
        if (screenInfo.isTablet && tablet != null) {
          return tablet!;
        }
        if (screenInfo.isDesktop && desktop != null) {
          return desktop!;
        }
        
        // Fallback logic
        return fallback ?? 
               desktop ?? 
               tablet ?? 
               mobile ?? 
               const SizedBox.shrink();
      },
    );
  }
}
