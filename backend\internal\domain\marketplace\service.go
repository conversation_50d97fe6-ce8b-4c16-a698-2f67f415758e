package marketplace

import (
	"quester-backend/internal/common/redis"
	"quester-backend/internal/domain/user"

	"github.com/google/uuid"
)

type Service interface {
	GetItems(limit, offset int) ([]Item, error)
	GetItem(id uuid.UUID) (*Item, error)
	GetCategories() ([]ItemCategory, error)
	GetCart(userID uuid.UUID) ([]CartItem, error)
	AddToCart(userID, itemID uuid.UUID, quantity int) error
	UpdateCartItem(userID, itemID uuid.UUID, quantity int) error
	RemoveFromCart(userID, itemID uuid.UUID) error
	PurchaseItem(userID, itemID uuid.UUID, quantity int) error
	PurchaseCart(userID uuid.UUID) error
	GetInventory(userID uuid.UUID) ([]UserInventory, error)
	EquipItem(userID, itemID uuid.UUID) error
	CreateItem(item *Item) error
	UpdateItem(item *Item) error
	DeleteItem(id uuid.UUID) error
}

type service struct {
	repo     Repository
	userRepo user.Repository
	redis    *redis.Client
}

func NewService(repo Repository, userRepo user.Repository, redisClient *redis.Client) Service {
	return &service{
		repo:     repo,
		userRepo: userRepo,
		redis:    redisClient,
	}
}

func (s *service) GetItems(limit, offset int) ([]Item, error) {
	return s.repo.GetItems(limit, offset)
}

func (s *service) GetItem(id uuid.UUID) (*Item, error) {
	return s.repo.GetItemByID(id)
}

func (s *service) GetCategories() ([]ItemCategory, error) {
	return s.repo.GetCategories()
}

func (s *service) GetCart(userID uuid.UUID) ([]CartItem, error) {
	return s.repo.GetCart(userID)
}

func (s *service) AddToCart(userID, itemID uuid.UUID, quantity int) error {
	// TODO: Implement cart logic
	return nil
}

func (s *service) UpdateCartItem(userID, itemID uuid.UUID, quantity int) error {
	// TODO: Implement cart update logic
	return nil
}

func (s *service) RemoveFromCart(userID, itemID uuid.UUID) error {
	return s.repo.RemoveFromCart(userID, itemID)
}

func (s *service) PurchaseItem(userID, itemID uuid.UUID, quantity int) error {
	// TODO: Implement purchase logic
	return nil
}

func (s *service) PurchaseCart(userID uuid.UUID) error {
	// TODO: Implement cart purchase logic
	return nil
}

func (s *service) GetInventory(userID uuid.UUID) ([]UserInventory, error) {
	return s.repo.GetInventory(userID)
}

func (s *service) EquipItem(userID, itemID uuid.UUID) error {
	// TODO: Implement item equipping logic
	return nil
}

func (s *service) CreateItem(item *Item) error {
	return s.repo.CreateItem(item)
}

func (s *service) UpdateItem(item *Item) error {
	return s.repo.UpdateItem(item)
}

func (s *service) DeleteItem(id uuid.UUID) error {
	return s.repo.DeleteItem(id)
}
