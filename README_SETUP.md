# Quester - Setup and Development Guide

This guide will help you set up and run the Quester application locally for development.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Docker** (version 20.0+) and **Docker Compose** (version 2.0+)
- **Go** (version 1.22+) for backend development
- **Flutter** (version 3.24+) for frontend development
- **Git** for version control

## Quick Start with Docker

The easiest way to run the entire application is using Docker Compose:

```bash
# Clone the repository
git clone <repository-url>
cd quester

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f
```

### Access Points

Once all services are running, you can access:

- **Frontend (Flutter Web)**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **API Documentation**: http://localhost:8080/docs (when implemented)
- **Database Admin (PgAdmin)**: http://localhost:5050
  - Email: <EMAIL>
  - Password: admin
- **Redis Admin**: http://localhost:8081

## Development Setup

### Backend Development

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Install dependencies**:
   ```bash
   go mod download
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start database and Redis** (if not using Docker):
   ```bash
   docker-compose up -d postgres redis
   ```

5. **Run the backend**:
   ```bash
   go run cmd/api/main.go
   ```

### Frontend Development

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   flutter pub get
   ```

3. **Generate code** (for JSON serialization, etc.):
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the frontend**:
   ```bash
   flutter run -d web-server --web-port 3000
   ```

## Project Structure

```
quester/
├── backend/                 # Go backend API
│   ├── cmd/api/            # Application entry point
│   ├── internal/           # Private application code
│   │   ├── domain/         # Domain-specific packages
│   │   ├── server/         # HTTP server setup
│   │   ├── middleware/     # HTTP middleware
│   │   └── common/         # Shared utilities
│   ├── pkg/                # Public utilities
│   ├── go.mod              # Go module file
│   └── .env                # Environment variables
├── frontend/               # Flutter frontend
│   ├── lib/                # Dart source code
│   │   ├── core/           # Core utilities and services
│   │   ├── features/       # Feature-specific code
│   │   ├── shared/         # Shared widgets and utilities
│   │   └── main.dart       # Application entry point
│   ├── pubspec.yaml        # Flutter dependencies
│   └── web/                # Web-specific files
├── docker-compose.yml      # Docker services configuration
└── README.md              # Project documentation
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register a new user
- `POST /api/v1/auth/login` - Login user
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - Logout user

### Users
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `GET /api/v1/users/statistics` - Get user statistics
- `GET /api/v1/users/activities` - Get user activities

### Quests
- `GET /api/v1/quests` - Get all quests
- `GET /api/v1/quests/:id` - Get quest by ID
- `POST /api/v1/quests/:id/start` - Start a quest
- `PUT /api/v1/quests/:id/progress` - Update quest progress
- `POST /api/v1/quests/:id/complete` - Complete a quest

### Achievements
- `GET /api/v1/achievements` - Get all achievements
- `GET /api/v1/achievements/user` - Get user achievements
- `POST /api/v1/achievements/:id/claim` - Claim achievement reward

### Marketplace
- `GET /api/v1/marketplace/items` - Get marketplace items
- `GET /api/v1/marketplace/cart` - Get user's cart
- `POST /api/v1/marketplace/cart` - Add item to cart
- `POST /api/v1/marketplace/purchase` - Purchase items

## Database Schema

The application uses PostgreSQL with the following main tables:

- **users** - User accounts and authentication
- **user_profiles** - User profile information
- **user_statistics** - User statistics and progress
- **quests** - Quest definitions
- **user_quests** - User quest progress
- **achievements** - Achievement definitions
- **user_achievements** - User achievement progress
- **marketplace_items** - Items available for purchase
- **notifications** - User notifications

## Environment Variables

### Backend (.env)
```env
# Server
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8080
GO_ENV=development

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=keshabalive
DATABASE_PASSWORD=9871
DATABASE_DATABASE=quester

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your-super-secret-jwt-key
```

## Development Commands

### Backend
```bash
# Run tests
go test ./...

# Build
go build -o bin/api cmd/api/main.go

# Format code
go fmt ./...

# Lint
golangci-lint run
```

### Frontend
```bash
# Run tests
flutter test

# Build for web
flutter build web

# Analyze code
flutter analyze

# Format code
dart format .
```

## Docker Commands

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs -f [service-name]

# Rebuild services
docker-compose build

# Reset everything (removes volumes)
docker-compose down -v
docker-compose up -d
```

## Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   # Check what's using the port
   lsof -i :8080
   # Kill the process or change the port in docker-compose.yml
   ```

2. **Database connection failed**:
   ```bash
   # Ensure PostgreSQL is running
   docker-compose ps postgres
   # Check logs
   docker-compose logs postgres
   ```

3. **Frontend build errors**:
   ```bash
   # Clean and rebuild
   flutter clean
   flutter pub get
   flutter packages pub run build_runner build --delete-conflicting-outputs
   ```

### Logs

- Backend logs: `docker-compose logs -f backend`
- Frontend logs: `docker-compose logs -f frontend`
- Database logs: `docker-compose logs -f postgres`
- Redis logs: `docker-compose logs -f redis`

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Run tests: `make test`
5. Commit your changes: `git commit -am 'Add new feature'`
6. Push to the branch: `git push origin feature/new-feature`
7. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
