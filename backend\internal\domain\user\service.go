package user

import (
	"fmt"

	"quester-backend/internal/common/redis"
	"quester-backend/internal/middleware"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type Service interface {
	// Authentication
	Register(req RegisterRequest) (*AuthResponse, error)
	Login(req LoginRequest) (*AuthResponse, error)
	RefreshToken(refreshToken string) (*AuthResponse, error)
	Logout(userID uuid.UUID) error

	// User management
	GetUserByID(id uuid.UUID) (*User, error)
	UpdateProfile(userID uuid.UUID, req UpdateProfileRequest) (*UserProfile, error)
	GetStatistics(userID uuid.UUID) (*UserStatistics, error)
	GetActivities(userID uuid.UUID, limit, offset int) ([]UserActivity, error)

	// Friends
	GetFriends(userID uuid.UUID) ([]User, error)
	AddFriend(userID, friendID uuid.UUID) error
	UpdateFriendship(userID, friendshipID uuid.UUID, status string) error
	RemoveFriend(userID, friendID uuid.UUID) error

	// Wallet
	GetWallet(userID uuid.UUID) (*WalletResponse, error)
	GetWalletTransactions(userID uuid.UUID, limit, offset int) ([]WalletTransaction, error)
	UpdateBalance(userID uuid.UUID, currency string, amount int, description string) error

	// Leaderboard
	GetLeaderboard(leaderboardType string, limit int) ([]LeaderboardEntry, error)
}

type service struct {
	repo      Repository
	redis     *redis.Client
	jwtSecret string
}

func NewService(repo Repository, redisClient *redis.Client, jwtSecret string) Service {
	return &service{
		repo:      repo,
		redis:     redisClient,
		jwtSecret: jwtSecret,
	}
}

// Authentication methods
func (s *service) Register(req RegisterRequest) (*AuthResponse, error) {
	// Check if user already exists
	if _, err := s.repo.GetUserByEmail(req.Email); err == nil {
		return nil, fmt.Errorf("user with email %s already exists", req.Email)
	}

	if _, err := s.repo.GetUserByUsername(req.Username); err == nil {
		return nil, fmt.Errorf("user with username %s already exists", req.Username)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &User{
		Email:    req.Email,
		Username: req.Username,
		Password: string(hashedPassword),
		IsActive: true,
		Role:     RoleUser,
	}

	if err := s.repo.CreateUser(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Create user profile
	profile := &UserProfile{
		UserID:      user.ID,
		DisplayName: req.Username,
		Level:       1,
		Experience:  0,
		Coins:       100, // Starting coins
		Gems:        0,
	}

	if err := s.repo.CreateProfile(profile); err != nil {
		return nil, fmt.Errorf("failed to create user profile: %w", err)
	}

	// Create user statistics
	stats := &UserStatistics{
		UserID: user.ID,
	}

	if err := s.repo.CreateStatistics(stats); err != nil {
		return nil, fmt.Errorf("failed to create user statistics: %w", err)
	}

	// Generate tokens
	accessToken, err := middleware.GenerateToken(user.ID, user.Username, user.Email, user.Role, s.jwtSecret, 24)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := middleware.GenerateRefreshToken(user.ID, s.jwtSecret)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return &AuthResponse{
		User:         *user,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    24 * 60 * 60, // 24 hours in seconds
	}, nil
}

func (s *service) Login(req LoginRequest) (*AuthResponse, error) {
	// Get user by email or username
	var user *User
	var err error

	if req.Email != "" {
		user, err = s.repo.GetUserByEmail(req.Email)
	} else {
		user, err = s.repo.GetUserByUsername(req.Username)
	}

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("invalid credentials")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if user is active
	if !user.IsActive {
		return nil, fmt.Errorf("user account is deactivated")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, fmt.Errorf("invalid credentials")
	}

	// Update login streak
	if err := s.repo.UpdateLoginStreak(user.ID); err != nil {
		// Log error but don't fail login
		fmt.Printf("Failed to update login streak: %v\n", err)
	}

	// Generate tokens
	accessToken, err := middleware.GenerateToken(user.ID, user.Username, user.Email, user.Role, s.jwtSecret, 24)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := middleware.GenerateRefreshToken(user.ID, s.jwtSecret)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return &AuthResponse{
		User:         *user,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    24 * 60 * 60, // 24 hours in seconds
	}, nil
}

func (s *service) RefreshToken(refreshToken string) (*AuthResponse, error) {
	// Validate refresh token
	userID, err := middleware.ValidateRefreshToken(refreshToken, s.jwtSecret)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Get user
	user, err := s.repo.GetUserByID(userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Generate new access token
	accessToken, err := middleware.GenerateToken(user.ID, user.Username, user.Email, user.Role, s.jwtSecret, 24)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	return &AuthResponse{
		User:         *user,
		AccessToken:  accessToken,
		RefreshToken: refreshToken, // Keep the same refresh token
		ExpiresIn:    24 * 60 * 60, // 24 hours in seconds
	}, nil
}

func (s *service) Logout(userID uuid.UUID) error {
	// In a real implementation, you might want to blacklist the token
	// For now, we'll just return success
	return nil
}

// User management methods
func (s *service) GetUserByID(id uuid.UUID) (*User, error) {
	return s.repo.GetUserByID(id)
}

func (s *service) UpdateProfile(userID uuid.UUID, req UpdateProfileRequest) (*UserProfile, error) {
	profile, err := s.repo.GetProfileByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}

	// Update fields
	if req.FirstName != nil {
		profile.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		profile.LastName = *req.LastName
	}
	if req.DisplayName != nil {
		profile.DisplayName = *req.DisplayName
	}
	if req.Bio != nil {
		profile.Bio = *req.Bio
	}
	if req.Location != nil {
		profile.Location = *req.Location
	}
	if req.Website != nil {
		profile.Website = *req.Website
	}
	if req.Avatar != nil {
		profile.Avatar = *req.Avatar
	}
	if req.Theme != nil {
		profile.Theme = *req.Theme
	}
	if req.Language != nil {
		profile.Language = *req.Language
	}
	if req.Timezone != nil {
		profile.Timezone = *req.Timezone
	}

	if err := s.repo.UpdateProfile(profile); err != nil {
		return nil, fmt.Errorf("failed to update profile: %w", err)
	}

	return profile, nil
}

func (s *service) GetStatistics(userID uuid.UUID) (*UserStatistics, error) {
	return s.repo.GetStatisticsByUserID(userID)
}

func (s *service) GetActivities(userID uuid.UUID, limit, offset int) ([]UserActivity, error) {
	return s.repo.GetActivitiesByUserID(userID, limit, offset)
}

// Friends methods
func (s *service) GetFriends(userID uuid.UUID) ([]User, error) {
	return s.repo.GetFriends(userID)
}

func (s *service) AddFriend(userID, friendID uuid.UUID) error {
	// Check if friendship already exists
	if _, err := s.repo.GetFriendship(userID, friendID); err == nil {
		return fmt.Errorf("friendship already exists")
	}

	// Create friend request
	friendship := &FriendRelationship{
		UserID:      userID,
		FriendID:    friendID,
		Status:      FriendStatusPending,
		RequestedBy: userID,
	}

	return s.repo.CreateFriendRequest(friendship)
}

func (s *service) UpdateFriendship(userID, friendshipID uuid.UUID, status string) error {
	return s.repo.UpdateFriendshipStatus(friendshipID, status)
}

func (s *service) RemoveFriend(userID, friendID uuid.UUID) error {
	return s.repo.DeleteFriendship(userID, friendID)
}

// Wallet methods
func (s *service) GetWallet(userID uuid.UUID) (*WalletResponse, error) {
	coins, gems, err := s.repo.GetUserBalance(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user balance: %w", err)
	}

	return &WalletResponse{
		Coins: coins,
		Gems:  gems,
	}, nil
}

func (s *service) GetWalletTransactions(userID uuid.UUID, limit, offset int) ([]WalletTransaction, error) {
	return s.repo.GetWalletTransactions(userID, limit, offset)
}

func (s *service) UpdateBalance(userID uuid.UUID, currency string, amount int, description string) error {
	// Get current balance
	coins, gems, err := s.repo.GetUserBalance(userID)
	if err != nil {
		return fmt.Errorf("failed to get current balance: %w", err)
	}

	// Calculate new balance
	var newBalance int
	if currency == "coins" {
		newBalance = coins + amount
	} else {
		newBalance = gems + amount
	}

	// Create transaction record
	transaction := &WalletTransaction{
		UserID:      userID,
		Type:        "earn",
		Currency:    currency,
		Amount:      amount,
		Balance:     newBalance,
		Description: description,
	}

	if amount < 0 {
		transaction.Type = "spend"
	}

	if err := s.repo.CreateWalletTransaction(transaction); err != nil {
		return fmt.Errorf("failed to create transaction: %w", err)
	}

	// Update balance
	return s.repo.UpdateUserBalance(userID, currency, amount)
}

// Leaderboard methods
func (s *service) GetLeaderboard(leaderboardType string, limit int) ([]LeaderboardEntry, error) {
	switch leaderboardType {
	case "experience":
		profiles, err := s.repo.GetTopUsersByExperience(limit)
		if err != nil {
			return nil, err
		}

		entries := make([]LeaderboardEntry, len(profiles))
		for i, profile := range profiles {
			entries[i] = LeaderboardEntry{
				Rank:        i + 1,
				UserID:      profile.UserID,
				DisplayName: profile.DisplayName,
				Avatar:      profile.Avatar,
				Score:       profile.Experience,
				Level:       profile.Level,
			}
		}
		return entries, nil

	case "quests":
		stats, err := s.repo.GetTopUsersByQuestsCompleted(limit)
		if err != nil {
			return nil, err
		}

		entries := make([]LeaderboardEntry, len(stats))
		for i, stat := range stats {
			// Get profile for display info
			profile, _ := s.repo.GetProfileByUserID(stat.UserID)
			entries[i] = LeaderboardEntry{
				Rank:        i + 1,
				UserID:      stat.UserID,
				DisplayName: profile.DisplayName,
				Avatar:      profile.Avatar,
				Score:       stat.QuestsCompleted,
				Level:       profile.Level,
			}
		}
		return entries, nil

	default:
		return nil, fmt.Errorf("invalid leaderboard type: %s", leaderboardType)
	}
}
