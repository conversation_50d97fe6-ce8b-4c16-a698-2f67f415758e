# Quester - Gamified Quest Management Platform

<div align="center">
  <img src="https://via.placeholder.com/200x200.png?text=Quester" alt="Quester Logo" width="200" height="200">
  <p><em>Transform ordinary tasks into epic quests and achievements</em></p>
</div>

## 🚀 Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd quester

# Start the development environment
docker-compose up -d

# Install Flutter dependencies
cd frontend
flutter pub get

# Run the Flutter app
flutter run -d web

# Access the application
# - Frontend: http://localhost:3000
# - Backend API: http://localhost:8080/api
# - PgAdmin: http://localhost:5050
# - Redis Commander: http://localhost:8081
```

## 🌟 Project Overview

Quester is a modern, full-stack gamified Role, Rank, Rewards, Quest, and Adventure management platform designed to increase user engagement through game-like elements and mechanics. It's built using cutting-edge technologies to provide a responsive, engaging, and feature-rich experience across web, mobile, and desktop platforms.

### 🎯 Purpose

- Convert routine activities into engaging quests and adventures
- Foster user engagement through gamification principles
- Create a reward-based achievement ecosystem
- Build community through shared challenges and leaderboards
- Provide real-time feedback and notification systems
- Integrate blockchain technology for transparent achievement tracking

### ✨ Key Features

- **User Management System**: Registration, authentication, profiles, and role-based access
- **Quest System**: Create, manage, start, and complete customizable quests with multiple steps
- **Achievement Framework**: Unlock achievements through quest completion and platform milestones
- **Virtual Economy**: Wallet system for earning, spending, and trading virtual currency
- **Reward Mechanisms**: Digital badges, points, levels, and virtual items
- **Marketplace**: Trade and purchase digital items and rewards (UI in FrontendApp, logic in BackendService)
- **Real-time Notifications**: In-app notifications and real-time updates via WebSockets
- **Social Features**: Leaderboards, friend systems, and collaborative quests
- **AI-Driven Personalization**: Custom quest recommendations based on user behavior
- **Blockchain Integration**: For NFT rewards and transparent achievement tracking

### 📊 Leaderboard System

Quester features a comprehensive leaderboard system that tracks user progress and achievements:

- **Global Rankings**: Overall rankings based on total points across the platform
- **Time-Based Filters**: View rankings by day, week, month, or all-time periods
- **Real-time Updates**: Leaderboards update in real-time through WebSocket notifications
- **Personal Rank Tracking**: Users can track their own position relative to other users
- **Category Leaderboards**: Specialized rankings for quests completed, achievements, and social connections
- **Visual Podium**: Interactive visual representation for top three positions
- **Pagination**: Efficient pagination for large leaderboard datasets
- **User Focus**: Ability to quickly locate your position in global rankings

### 📡 WebSocket Implementation

Quester includes a robust WebSocket implementation for real-time communication:

- **Real-time notifications**: Achievements, quest updates, and system messages
- **Live updates**: Leaderboard changes, friend activities, and marketplace updates
- **Interactive features**: Chat, collaborative quests, and real-time feedback
- **Secure communication**: JWT authentication for WebSocket connections
- **Efficient messaging**: Targeted user messages and broadcast capabilities


## 🔐 User Roles And Permissions

Quester implements a Role-Based Access Control (RBAC) system to manage user capabilities and access to different features.

### Roles

-   **User (Default)**: Standard users who can participate in quests, earn achievements, manage their profile, use the marketplace, and interact with social features.
-   **Admin**: Full access to all features, including user management, quest management, and achievement management.
-   **Moderator**: Moderates quests, achievements, and user interactions.
-   **Developer**: Full access to all features, including user management, quest management, and achievement management.
-   **Custom Roles**: Create custom roles with specific permissions as needed.

### Permissions

-   **User**:
    -   View quests, achievements, and leaderboards
    -   Join quests
    -   Complete quests
    -   Earn achievements
    -   Manage their profile
    -   Use the marketplace
    -   Interact with social features  
-   **Admin**:
    -   Full access to all features:
    -   User management
    -   Quest management
    -   Achievement management
-   **Moderator**:
    -   Moderates quests, achievements, and user interactions
    -   View quests, achievements, and leaderboards
    -   Join quests
    -   Complete quests
    -   Earn achievements
    -   Manage their profile
    -   Use the marketplace
    -   Interact with social features
-   **Developer**:
    -   Full access to all features:
    -   User management
    -   Quest management
    -   Achievement management
    -   Virtual economy
    -   Marketplace
    -   Real-time notifications
    -   Social features
    -   Marketplace
-   **Custom Roles**:
    -   Create custom roles with specific permissions as needed.


## 🔐 Authentication And Authorization

Quester uses JSON Web Tokens (JWT) for authentication and authorization. The JWT is stored in a cookie and is sent with every request. The backend validates the JWT and returns a new one if it's expired.

### JWT Structure

The JWT contains the following claims:

-   `sub`: User ID
-   `iat`: Issued Atr ID
-   `exp`: Expiration Time
-   `roles`: User roles
-   `permissions`: User permissions
-   `aud`: Audience
-   `iss`: Issuer
-   `nbf`: Not Before
-   `jti`: JWT ID


### Permissions System

-   **Backend**:
    -   Permissions are enforced at the API level using middleware and service layer checks.
    -   Each API endpoint requires specific permissions associated with user roles.
    -   The backend validates the user's JWT token and associated roles/permissions before granting access to resources or actions.
    -   GORM scopes can be used for filtering data based on user roles (e.g., an admin sees all quests, a user only sees published ones).
-   **Frontend**:
    -   The frontend dynamically adjusts the UI based on the logged-in user's role and permissions.
    -   Vue Router navigation guards restrict access to specific routes (e.g., admin panels).
    -   Components conditionally render elements (buttons, menu items) based on permissions fetched from the backend or stored in the user's state (Pinia store).
    -   Sensitive actions are always re-validated by the backend, even if the UI element was visible.


## 🚀 Tech Stack (2025)

### Frontend

- **Framework**: [Flutter 3.8+](https://flutter.dev/) - Google's UI toolkit for natively compiled applications
- **Language**: [Dart 3.0+](https://dart.dev/) - Optimized for UI development with null safety
- **State Management**: [Riverpod 2.4+](https://riverpod.dev/) with [Provider 6.1+](https://pub.dev/packages/provider)
- **Navigation**: [GoRouter 15.1+](https://pub.dev/packages/go_router) for declarative routing
- **HTTP Client**: [Dio 5.4+](https://pub.dev/packages/dio) with interceptors and error handling
- **Real-time Communication**: [WebSocket Channel 3.0+](https://pub.dev/packages/web_socket_channel)
- **Local Storage**: [Hive 2.2+](https://pub.dev/packages/hive) with [Shared Preferences 2.5+](https://pub.dev/packages/shared_preferences)
- **UI Components**: Material Design 3 with [Google Fonts 6.1+](https://pub.dev/packages/google_fonts)
- **Animations**: [Lottie 3.0+](https://pub.dev/packages/lottie) and [Flutter Staggered Animations 1.1+](https://pub.dev/packages/flutter_staggered_animations)
- **Testing**: Flutter Test framework with widget and integration testing
- **Build Tools**: Flutter SDK with multi-platform support (iOS, Android, Web, Desktop)
- **Development Tools**: Hot reload, Flutter DevTools, VS Code/Android Studio extensions

### Backend

- **Language**: [Go 1.22+](https://go.dev/)
- **Web Framework**: [Fiber v2.52+](https://gofiber.io/)
- **ORM**: [GORM v1.30+](https://gorm.io/) with PostgreSQL driver
- **Authentication**: JWT with [golang-jwt/jwt v5.2+](https://github.com/golang-jwt/jwt)
- **API Design**: RESTful endpoints with middleware chain
- **Real-time Communication**: WebSockets with [Fiber WebSocket v1.3+](https://github.com/gofiber/contrib/websocket)
- **Caching**: [Redis v9.9+](https://redis.io/) with go-redis client
- **Database**: [PostgreSQL](https://www.postgresql.org/) with GORM integration
- **Security**: Input validation, JWT middleware, CORS configuration
- **Environment**: [GoDotEnv v1.5+](https://github.com/joho/godotenv) for configuration

### Infrastructure

- **Containerization**: [Docker](https://www.docker.com/), Docker Compose, [Kubernetes](https://kubernetes.io/)
- **Web Server**: [Nginx](https://nginx.org/) with HTTP/3 support
- **Caching**: Redis cluster with persistence
- **Database**: PostgreSQL with replication
- **Search**: Optional Elasticsearch integration
- **Monitoring**: [Prometheus](https://prometheus.io/), [Grafana](https://grafana.com/), OpenTelemetry
- **CI/CD**: GitHub Actions, ArgoCD
- **Cloud Providers**: AWS, GCP, Azure compatibility
- **Optional**: Blockchain integration (Ethereum/Solana)

## 🏗️ Architecture

Quester follows modern mobile-first architecture principles with clean separation of concerns:

**Note**: The frontend is built using Flutter, Google's powerful cross-platform framework that compiles to native code for iOS, Android, Web, and Desktop platforms, providing consistent UI and performance across all targets.

<div align="center">
  <img src="https://via.placeholder.com/800x400.png?text=Quester+Architecture" alt="Architecture Overview">
</div>

### Frontend Architecture

- **Widget-based**: Everything is a widget - structural, visual, interactive, and layout widgets
- **Cross-platform**: Native compilation with consistent UI across all platforms
- **Native Performance**: Direct compilation to ARM code eliminating JavaScript bridge
- **Flutter Engine**: Custom rendering using Skia graphics engine
- **Reactive Programming**: Declarative UI with state management patterns
- **Responsive Design**: Adaptive layouts with MediaQuery and LayoutBuilder
- **Hot Reload**: Fast development iteration with stateful hot reload

### Backend Architecture

- **Layered Design**: Handlers → Services → Repositories → Models
- **Domain-Driven**: Business logic encapsulated in service layer
- **Clean Architecture**: Dependencies point inward
- **API Gateway**: Central entry point with middleware chain
- **Microservices Ready**: Core services with clear boundaries
- **Event-Driven**: Message broker for asynchronous operations
- **Idempotent Operations**: Safe retries and conflict resolution

### Data Flow

- Frontend uses Flutter widgets to build native interfaces across all platforms
- HTTP requests processed through Dio HTTP client with custom interceptors
- API requests processed through middleware chain
- Backend services handle business logic and domain rules
- Repository layer manages data access and persistence
- WebSockets provide real-time updates and notifications (NotificationSystem uses Redis pub/sub for backend distribution)
- Redis caches frequently accessed data

## 🛠️ Development Environment

### Prerequisites

- Docker 25.0+ and Docker Compose v2.24+
- Flutter SDK 3.19+ ([Install Flutter](https://docs.flutter.dev/get-started/install))
- Dart SDK 3.3+ (included with Flutter)
- Go 1.22+
- Git 2.43+
- Make (optional, for Makefile commands)
- VS Code with Flutter extension or Android Studio with Flutter plugin
- iOS Simulator (macOS) or Android Studio/Android SDK (for mobile development)

### Quick Start

```bash
# Clone the repository
git clone https://quester.git
cd quester

# Create environment file
cp .env.example .env

# Start the development environment
docker-compose up -d

# Install Flutter dependencies (if developing frontend)
cd frontend
flutter pub get

# Start Flutter development
flutter run
# For web development
flutter run -d web
# For specific device
flutter run -d <device_id>

# Access points:
# - Frontend (Mobile): Physical device or simulator
# - Frontend (Web): http://localhost:auto-assigned-port
# - Backend API: http://localhost:8080/api
# - API Documentation: http://localhost:8080/api/docs
# - PgAdmin: http://localhost:5050
# - Redis Commander: http://localhost:8081
```

### Design System

The User Guidelines document (USER_GUIDELINES.md) contains comprehensive design principles and component guidelines:

- **Core Design Principles**: Modern & Clean Design, Immersive Gamification, Responsive & Adaptive Design, Accessibility First, Cohesive Design Language
- **Design Tokens**: Colors, Typography, Spacing, Borders, Shadows
- **Component Guidelines**: Buttons, Cards, Forms, Data Display, Navigation, Notifications
- **Responsive Design**: Breakpoints, Patterns, Implementation Guide
- **Animation Guidelines**: Principles, Common Animations, Implementation
- **Accessibility**: WCAG 2.2 AA compliance, keyboard navigation, screen reader support
- **Dark Mode**: Complete theme support with color mapping

### Technology-specific Documentation

- **Flutter Framework**: [https://docs.flutter.dev/](https://docs.flutter.dev/)
- **Dart Language**: [https://dart.dev/guides](https://dart.dev/guides)
- **Flutter Widgets**: [https://docs.flutter.dev/ui/widgets](https://docs.flutter.dev/ui/widgets)
- **Flutter Navigation**: [https://docs.flutter.dev/ui/navigation](https://docs.flutter.dev/ui/navigation)
- **Flutter State Management**: [https://docs.flutter.dev/data-and-backend/state-mgmt](https://docs.flutter.dev/data-and-backend/state-mgmt)
- **Go Language**: [https://go.dev/doc/](https://go.dev/doc/)
- **Fiber Framework**: [https://pkg.go.dev/github.com/gofiber/fiber/v2](https://pkg.go.dev/github.com/gofiber/fiber/v2)
- **Fiber WebSocket**: [https://pkg.go.dev/github.com/gofiber/contrib/websocket](https://pkg.go.dev/github.com/gofiber/contrib/websocket)
- **GORM ORM**: [https://pkg.go.dev/gorm.io/gorm](https://pkg.go.dev/gorm.io/gorm)

## 📁 Project Structure

### Frontend Structure (Flutter)

```
frontend/
├── android/                # Android platform-specific code
├── ios/                    # iOS platform-specific code
├── web/                    # Web platform-specific code
├── windows/                # Windows platform-specific code
├── linux/                  # Linux platform-specific code
├── macos/                  # macOS platform-specific code
├── assets/                 # Static assets
│   ├── images/             # Image assets
│   ├── icons/              # Icon assets
│   ├── animations/         # Lottie animation files
│   └── fonts/              # Custom fonts
├── lib/                    # Main Dart source code
│   ├── core/               # Core functionality
│   │   ├── constants/      # App-wide constants
│   │   ├── config/         # Configuration files
│   │   ├── models/         # Data models with JSON serialization
│   │   ├── services/       # Core services (API, Auth, Storage)
│   │   ├── providers/      # Riverpod providers
│   │   ├── theme/          # App theming and styling
│   │   ├── utils/          # Utility functions
│   │   └── core.dart       # Core exports
│   ├── features/           # Feature modules
│   │   ├── authentication/ # Login, register, forgot password
│   │   ├── dashboard/      # Main dashboard and navigation
│   │   ├── quests/         # Quest listing and details
│   │   ├── achievements/   # Achievement tracking
│   │   ├── wallet/         # Wallet and transactions
│   │   ├── marketplace/    # Item marketplace
│   │   ├── notifications/  # Notification management
│   │   ├── leaderboard/    # User rankings
│   │   ├── profile/        # User profile management
│   │   ├── settings/       # App settings
│   │   └── features.dart   # Feature exports
│   ├── shared/             # Shared components
│   │   ├── widgets/        # Reusable UI widgets
│   │   └── shared.dart     # Shared exports
│   └── main.dart           # Application entry point
├── test/                   # Test files
├── pubspec.yaml            # Flutter package configuration
├── analysis_options.yaml  # Dart analyzer configuration
└── README.md               # Frontend documentation
```

### Backend Structure (Go)

```
backend/
├── cmd/
│   └── api/                # API server entry point
│       └── main.go
├── internal/               # Private application code
│   ├── config/             # Configuration management
│   │   └── config.go
│   ├── database/           # Database connection & initialization
│   │   └── database.go
│   ├── handlers/           # HTTP request handlers
│   │   ├── achievement.go  # Achievement endpoints
│   │   ├── leaderboard.go  # Leaderboard endpoints
│   │   ├── marketplace.go  # Marketplace endpoints
│   │   ├── notification.go # Notification endpoints
│   │   ├── quest.go        # Quest endpoints
│   │   ├── realtime.go     # Real-time features
│   │   ├── role.go         # Role management
│   │   ├── routes.go       # Route definitions
│   │   ├── user.go         # User endpoints
│   │   ├── wallet.go       # Wallet endpoints
│   │   └── websocket.go    # WebSocket handling
│   ├── middleware/         # Custom middleware
│   │   └── auth.go         # Authentication middleware
│   └── models/             # Database models
│       └── models.go       # All data models
├── config/                 # Configuration files
│   └── docker.env          # Docker environment variables
├── go.mod                  # Go module file
├── go.sum                  # Go module checksum file
└── README.md               # Backend documentation
```

## 🔌 API Documentation
## 🚀 Development Commands

### Frontend Development (Flutter)

```bash
cd frontend

# Install dependencies
flutter pub get

# Run code generation (for models)
flutter packages pub run build_runner build

# Development
flutter run                       # Run on connected device
flutter run -d web               # Run on web browser
flutter run -d chrome            # Run specifically on Chrome
flutter run -d <device_id>       # Run on specific device

# Testing
flutter test                     # Run unit tests
flutter test test/widget_test.dart # Run specific test

# Code quality
flutter analyze                  # Analyze code for issues
flutter format .                 # Format code

# Building
flutter build web                # Build for web
flutter build apk               # Build Android APK
flutter build ios              # Build for iOS
flutter build windows          # Build for Windows
```

### Backend Development (Go)

```bash
cd backend

# Install dependencies
go mod download
go mod tidy

# Development
go run cmd/api/main.go          # Run development server

# Building
go build -o api.exe cmd/api     # Build executable (Windows)
go build -o api cmd/api         # Build executable (Linux/macOS)

# Testing
go test ./...                   # Run all tests
go test -v ./internal/handlers  # Run specific package tests

# Code quality
go fmt ./...                    # Format code
go vet ./...                    # Vet code for issues
```

### Docker Development

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs frontend
docker-compose logs backend
docker-compose logs postgres
docker-compose logs redis

# Rebuild services
docker-compose build frontend
docker-compose build backend

# Access points:
# - Frontend (Web): http://localhost:3000
# - Backend API: http://localhost:8080/api
# - PgAdmin: http://localhost:5050
# - Redis Commander: http://localhost:8081
```

Quester provides comprehensive API documentation through OpenAPI (Swagger) specification available at `http://localhost:8080/api/docs` when running in development mode.

### Core API Endpoints

#### Authentication Endpoints
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `POST /api/auth/logout` - Logout a user
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/reset-password` - Request password reset
- `POST /api/auth/reset-password/:token` - Reset password with token
- `POST /api/auth/oauth/:provider` - OAuth authentication
- `GET /api/auth/oauth/:provider/callback` - OAuth callback

#### User Endpoints
- `GET /api/users/profile` - Get current user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/statistics` - Get user statistics
- `GET /api/users/activity` - Get user activity
- `GET /api/users/leaderboard` - Get user leaderboard
- `GET /api/users/friends` - Get user friends
- `POST /api/users/friends/:id` - Add friend
- `DELETE /api/users/friends/:id` - Remove friend

#### Quest Endpoints
- `GET /api/quests` - Get all quests
- `GET /api/quests/:id` - Get quest by ID
- `POST /api/quests` - Create a new quest
- `PUT /api/quests/:id` - Update a quest
- `DELETE /api/quests/:id` - Delete a quest
- `GET /api/quests/categories` - Get quest categories
- `GET /api/quests/category/:id` - Get quests by category
- `GET /api/quests/recommended` - Get recommended quests
- `POST /api/quests/:id/start` - Start a quest
- `POST /api/quests/:id/progress` - Update quest progress
- `POST /api/quests/:id/complete` - Complete a quest
- `POST /api/quests/:id/claim` - Claim quest reward

#### Achievement Endpoints
- `GET /api/achievements` - Get all achievements
- `GET /api/achievements/:id` - Get achievement by ID
- `GET /api/achievements/categories` - Get achievement categories
- `GET /api/achievements/statistics` - Get achievement statistics
- `POST /api/achievements/:id/claim` - Claim achievement reward

#### Marketplace Endpoints
- `GET /api/marketplace/items` - Get all marketplace items
- `GET /api/marketplace/items/:id` - Get marketplace item by ID
- `POST /api/marketplace/items` - Create a new item
- `PUT /api/marketplace/items/:id` - Update an item
- `DELETE /api/marketplace/items/:id` - Delete an item
- `GET /api/marketplace/categories` - Get marketplace categories
- `GET /api/marketplace/categories/:id/items` - Get items by category
- `GET /api/marketplace/cart` - Get cart items
- `POST /api/marketplace/cart` - Add item to cart
- `PUT /api/marketplace/cart/:id` - Update cart item quantity
- `DELETE /api/marketplace/cart/:id` - Remove item from cart
- `POST /api/marketplace/purchase` - Purchase an item
- `POST /api/marketplace/purchase/cart` - Purchase all items in cart

#### Notification Endpoints
- `GET /api/notifications` - Get all notifications
- `GET /api/notifications/:id` - Get notification by ID
- `GET /api/notifications/unread/count` - Get unread notifications count
- `PUT /api/notifications/:id/read` - Mark notification as read
- `PUT /api/notifications/read/all` - Mark all notifications as read
- `DELETE /api/notifications/:id` - Delete notification
- `DELETE /api/notifications/all` - Delete all notifications

#### Leaderboard Endpoints
- `GET /api/leaderboard` - Get global leaderboard with pagination
  - Query parameters:
    - `time_frame`: Filter by time period (`all_time`, `daily`, `weekly`, `monthly`)
    - `page`: Page number (default: 1)
    - `limit`: Items per page (default: 100)
- `GET /api/leaderboard/me` - Get current user's rank with surrounding entries
  - Query parameters:
    - `time_frame`: Filter by time period (`all_time`, `daily`, `weekly`, `monthly`)
    - `limit`: Number of entries around user's position (default: 5)

### WebSocket Events
- `notification` - New notification
- `unread_count` - Update unread notifications count
- `quest_progress` - Quest progress update
- `achievement_unlocked` - Achievement unlocked
- `wallet_updated` - Wallet balance updated
- `leaderboard_update` - Leaderboard data updated
  - Data structure:
    ```json
    {
      "time_frame": "all_time|daily|weekly|monthly",
      "updated_at": "2025-05-15T12:34:56Z",
      "message": "Leaderboard updated"
    }
    ```
- `leaderboard_updated` - Leaderboard position changed
- `friend_activity` - Friend activity update

## 💾 Database Schema

Quester uses PostgreSQL for persistent storage with the following core data models:

### Core Data Models
- **Users**: User accounts, profiles, and authentication
- **Quests**: Quest definitions, categories, and reward structures
- **Quest Steps**: Individual steps within quests with completion criteria
- **User Quests**: Relationship between users and their active/completed quests
- **Achievements**: Achievement definitions, categories, and criteria
- **Achievement Rewards**: Rewards associated with achievements
- **User Achievements**: Relationship between users and their unlocked achievements
- **Marketplace Items**: Virtual items available for purchase
- **User Purchases**: Record of user marketplace transactions
- **Notifications**: User notifications and their status
- **Friend Relationships**: User friendship connections
- **Leaderboards**: Global and category-specific leaderboards
- **Wallet Transactions**: Record of all virtual currency transactions

## 🗺️ Roadmap

### Phase 1: Core Platform (Completed)
- User management and authentication
- Basic quest system
- Achievement system
- Virtual wallet
- Notifications
- Basic statistics

### Phase 2: Enhanced Features (Current)
- Marketplace implementation
- Advanced quest types (daily, weekly, chain, etc.)
- Social features (friends, leaderboards)
- Improved statistics and analytics
- Mobile responsiveness improvements
- API documentation and developer portal

### Phase 3: Advanced Features (Next)
- AI-driven personalization and recommendations
- Collaborative quests and team challenges
- Advanced gamification elements
- Expanded social features
- Enhanced analytics dashboard
- Performance optimizations
- Advanced Flutter widgets and animations
- Platform-specific optimizations and native integrations

### Phase 4: Enterprise & Integration (Future)
- Blockchain integration for NFT rewards
- Third-party API integrations
- Enterprise SSO options
- Multi-tenant architecture
- White-label solutions
- Advanced customization options
- Open API for developer extensions
- App Store and Google Play distribution with Flutter's native compilation

## 🧪 Testing Strategy

Quester implements a comprehensive testing strategy across all layers of the application:

### Frontend Testing (Flutter)
- **Unit Tests**: Business logic and utility function testing with Flutter Test
- **Widget Tests**: UI component testing with Flutter Widget Testing
- **Integration Tests**: Multi-screen flow testing with Flutter Integration Testing
- **Golden Tests**: UI regression testing with screenshot comparison
- **Performance Tests**: Widget performance and memory usage testing
- **Accessibility Testing**: Semantic testing for screen readers

### Backend Testing (Go)
- **Unit Tests**: Function and service testing with Go testing package
- **Integration Tests**: API endpoint testing with test database
- **Benchmark Tests**: Performance testing with Go benchmark tools
- **Table-driven Tests**: Comprehensive test case coverage
- **Mock Testing**: Service mocking with testify/mock

### Testing Commands

```bash
# Frontend testing
cd frontend
flutter test                    # Run all tests
flutter test --coverage        # Run tests with coverage
flutter test test/widget_test.dart # Run specific test file

# Backend testing
cd backend
go test ./...                   # Run all tests
go test -v ./internal/handlers  # Run with verbose output
go test -cover ./...            # Run with coverage
go test -bench=.                # Run benchmark tests
```

### Testing Tools
- **CI Integration**: Automated test runs on pull requests
- **Coverage Reports**: Test coverage tracking for both Flutter and Go
- **Mocking**: Service and API mocking for isolated testing
- **Test Data**: Fixtures and factories for consistent test data

## 🚀 Current Implementation Status

### Frontend (Flutter) - ✅ Implemented
- **✅ Core Architecture**: Modular Flutter project with clean architecture
- **✅ State Management**: Riverpod providers and state management setup
- **✅ Navigation**: GoRouter with declarative routing configuration
- **✅ UI Framework**: Material Design 3 with custom theming and Google Fonts
- **✅ Local Storage**: Hive integration with shared preferences
- **✅ HTTP Client**: Dio setup with interceptors and error handling
- **✅ WebSocket**: WebSocket channel integration for real-time features
- **✅ Feature Modules**: Complete feature structure with:
  - Authentication (login, register, forgot password)
  - Dashboard with bottom navigation
  - Quests management and tracking
  - Achievements system
  - Wallet and transactions
  - Marketplace functionality
  - Notifications management
  - Leaderboard with rankings
  - User profile management
  - Settings and preferences
- **✅ Shared Components**: Reusable widgets and UI components
- **✅ Models**: JSON serializable data models with code generation
- **✅ Testing**: Basic test structure and widget tests
- **✅ Multi-platform**: Support for Web, Android, iOS, Windows, macOS, Linux

### Backend (Go) - ✅ Core Infrastructure Complete
- **✅ Core Framework**: Fiber v2 web framework with middleware chain
- **✅ Database**: GORM integration with PostgreSQL
- **✅ Authentication**: JWT middleware and authentication handlers
- **✅ WebSocket**: Real-time communication with WebSocket support
- **✅ Redis Integration**: Caching and pub/sub functionality
- **✅ API Structure**: RESTful endpoints with proper routing
- **✅ Data Models**: Complete database models for all entities
- **✅ Handlers**: API endpoint handlers for all major features:
  - User management and authentication
  - Quest system with CRUD operations
  - Achievement tracking and management
  - Wallet and transaction handling
  - Marketplace item management
  - Notification system
  - Leaderboard functionality
  - Real-time features and WebSocket handling
- **✅ Configuration**: Environment-based configuration management
- **✅ Docker Support**: Complete Docker Compose setup

### Infrastructure - ✅ Development Ready
- **✅ Docker Compose**: Complete development environment
- **✅ Database**: PostgreSQL with PgAdmin for administration
- **✅ Caching**: Redis with Redis Commander for management
- **✅ Environment Configuration**: Flexible environment variable setup
- **✅ Development Tools**: Hot reload and development optimization

### Pending Implementation
- **🔄 Business Logic**: Handler implementations need full business logic
- **🔄 Data Validation**: Input validation and error handling
- **🔄 API Documentation**: Swagger/OpenAPI documentation
- **🔄 Testing**: Comprehensive test coverage
- **🔄 Security**: Enhanced security measures and rate limiting
- **🔄 Performance**: Optimization and caching strategies

## 🚢 Deployment

Quester supports flexible deployment options adapted to different environments:

### Development Environment
- **Docker Compose**: Complete development stack with hot reload
- **Flutter Hot Reload**: Instant UI updates during development
- **Go Air**: Live reload for backend development (optional)
- **Local Services**: PostgreSQL, Redis, and PgAdmin containers
- **Development Tools**: Redis Commander for cache inspection

### Docker Services
The project includes a comprehensive Docker Compose setup:

```yaml
services:
  frontend:     # Flutter web application
  backend:      # Go Fiber API server
  postgres:     # PostgreSQL database
  redis:        # Redis cache and pub/sub
  pgadmin:      # Database administration
  redis-commander: # Redis administration
```

### Environment Configuration
- **Environment Variables**: Configurable through `.env` file
- **Docker Environment**: Separate configuration for containerized deployment
- **Multi-stage Builds**: Optimized Docker images for development and production
- **Volume Mounting**: Persistent data and development file mounting

### Production Considerations
- **Flutter Web**: Optimized web builds with tree shaking
- **Go Binary**: Compiled binary for efficient deployment
- **Database**: PostgreSQL with connection pooling
- **Caching**: Redis cluster for high availability
- **Monitoring**: Health checks and logging integration
- **Security**: JWT authentication and CORS configuration

## 📚 Technology Documentation Resources to Follow and memorised by AI Models

### Frontend Resources
- **Flutter Framework**: [https://docs.flutter.dev/](https://docs.flutter.dev/)
- **Dart Language**: [https://dart.dev/](https://dart.dev/)
- **Flutter Installation**: [https://docs.flutter.dev/get-started/install](https://docs.flutter.dev/get-started/install)
- **Flutter Widgets Catalog**: [https://docs.flutter.dev/ui/widgets](https://docs.flutter.dev/ui/widgets)
- **Flutter Navigation & Routing**: [https://docs.flutter.dev/ui/navigation](https://docs.flutter.dev/ui/navigation)
- **Flutter State Management**: [https://docs.flutter.dev/data-and-backend/state-mgmt](https://docs.flutter.dev/data-and-backend/state-mgmt)
- **Flutter Testing**: [https://docs.flutter.dev/testing](https://docs.flutter.dev/testing)
- **Flutter Performance**: [https://docs.flutter.dev/perf](https://docs.flutter.dev/perf)

### Backend Resources
- **Go Language**: [https://go.dev/doc/](https://go.dev/doc/)
- **Fiber Framework**: [https://docs.gofiber.io/](https://docs.gofiber.io/)
- **Fiber API Reference**: [https://pkg.go.dev/github.com/gofiber/fiber/v2](https://pkg.go.dev/github.com/gofiber/fiber/v2)
- **Fiber WebSocket**: [https://pkg.go.dev/github.com/gofiber/contrib/websocket](https://pkg.go.dev/github.com/gofiber/contrib/websocket)
- **GORM ORM**: [https://gorm.io/docs/](https://gorm.io/docs/)
- **GORM API Reference**: [https://pkg.go.dev/gorm.io/gorm](https://pkg.go.dev/gorm.io/gorm)
- **PostgreSQL**: [https://www.postgresql.org/docs/](https://www.postgresql.org/docs/)
- **Redis**: [https://redis.io/documentation](https://redis.io/documentation)

## 📦 Dependencies

### Frontend Dependencies (Flutter)

#### Core Dependencies
```yaml
# Framework
flutter: sdk: flutter
cupertino_icons: ^1.0.8

# HTTP and Networking
dio: ^5.4.0                    # HTTP client with interceptors
web_socket_channel: ^3.0.3     # WebSocket communication

# State Management
provider: ^6.1.1               # Provider pattern
riverpod: ^2.4.9              # Riverpod state management
flutter_riverpod: ^2.4.9      # Flutter integration for Riverpod

# Navigation
go_router: ^15.1.2            # Declarative routing

# UI and Styling
google_fonts: ^6.1.0          # Google Fonts integration
flutter_svg: ^2.0.9           # SVG support
cached_network_image: ^3.4.1   # Image caching
lottie: ^3.0.0                # Lottie animations
flutter_staggered_animations: ^1.1.1  # Staggered animations
shimmer: ^3.0.0               # Shimmer loading effects

# Local Storage
shared_preferences: ^2.5.3    # Simple key-value storage
hive: ^2.2.3                  # NoSQL database
hive_flutter: ^1.1.0          # Flutter integration for Hive
flutter_secure_storage: ^9.0.0 # Secure storage

# Utilities
uuid: ^4.3.3                  # UUID generation
url_launcher: ^6.2.4          # URL launching
crypto: ^3.0.3                # Cryptographic functions
email_validator: ^3.0.0       # Email validation

# Data Visualization
fl_chart: ^1.0.0              # Charts and graphs

# Platform Information
device_info_plus: ^11.4.0     # Device information
package_info_plus: ^8.3.0     # Package information

# Image Handling
image_picker: ^1.0.7          # Image picker
flutter_image_compress: ^2.4.0 # Image compression

# JSON Serialization
json_annotation: ^4.9.0       # JSON annotations
```

#### Development Dependencies
```yaml
flutter_test: sdk: flutter
flutter_lints: ^6.0.0         # Linting rules
hive_generator: ^2.0.1        # Code generation for Hive
build_runner: ^2.4.15         # Code generation runner
json_serializable: ^6.9.0     # JSON serialization generator
```

### Backend Dependencies (Go)

#### Core Dependencies
```go
// Web Framework
github.com/gofiber/fiber/v2 v2.52.8
github.com/gofiber/contrib/websocket v1.3.2

// Database
gorm.io/gorm v1.30.0
gorm.io/driver/postgres v1.6.0

// Authentication
github.com/golang-jwt/jwt/v5 v5.2.2
golang.org/x/crypto v0.31.0

// Caching and Pub/Sub
github.com/redis/go-redis/v9 v9.9.0

// Utilities
github.com/google/uuid v1.6.0
github.com/joho/godotenv v1.5.1
```

## 🧠 Memory Management System

The Quester platform employs a dual-layer persistent memory system designed to optimize data access patterns and relationship modeling:

1. **Persistent Memory**: A key-value store with TTL support for caching and ephemeral data
2. **Knowledge Graph**: A graph database structure for modeling entities and their relationships

This architecture provides high performance for both traditional key-value lookups and complex relationship-based queries.

### System Architecture

```
┌─────────────────────────────────────┐
│           Application Layer         │
└───────────────┬─────────────────────┘
                │
┌───────────────▼─────────────────────┐
│         Memory Manager Service      │
│                                     │
│  ┌─────────────┐   ┌──────────────┐ │
│  │  Persistent │   │  Knowledge   │ │
│  │    Memory   │   │    Graph     │ │
│  └─────────────┘   └──────────────┘ │
└───────────────┬─────────────────────┘
                │
┌───────────────▼─────────────────────┐
│         Storage Adapters            │
│                                     │
│  ┌─────────────┐   ┌──────────────┐ │
│  │   File      │   │    Redis     │ │
│  │  Storage    │   │   Storage    │ │
│  └─────────────┘   └──────────────┘ │
└─────────────────────────────────────┘
```

### Persistent Memory

The persistent memory component provides a key-value store with TTL support used for:

- **Caching**: Store frequently accessed data to reduce database load
- **Session Management**: Track user sessions and authentication state
- **Configuration**: Maintain feature flags and system settings
- **Analytics**: Store user activity metrics and statistics
- **Ephemeral Data**: Any data with a limited lifespan

### Knowledge Graph

The knowledge graph component provides a graph database structure optimized for:

- **Social Connections**: Model followers, friends, and social interactions
- **Quest System**: Track participation, progress, and completion of quests
- **Achievement System**: Manage achievement unlocks and progress tracking
- **Marketplace**: Record ownership and transactions of virtual items
- **Recommendation Engine**: Analyze user behavior patterns and relationships
- **Complex Queries**: Efficiently traverse entity relationships

### Core Entity Types

The knowledge graph consists of these primary entity types:

- **User**: User profiles and accounts
- **Quest**: Quest definitions and metadata
- **Achievement**: Achievement definitions
- **Item**: Virtual items and assets  
- **Transaction**: Economic transactions
- **Leaderboard**: Leaderboard snapshots

### Key Relationships

The system models relationships including:

- User `creates` Quest
- User `participates` in Quest
- User `completes` Quest  
- User `unlocks` Achievement
- User `follows` User
- User `owns` Item
- User `performs` Transaction
- Transaction `references` Item
- Leaderboard `ranks` User

### Setup and Usage

1. **Initialization**: The memory system automatically initializes when the application starts.

2. **Development Environment**:
   - Memory files are stored in the project root:
     - `persistent_memory.json` - Persistent memory storage
     - `knowledge_graph.json` - Knowledge graph storage

3. **Production Environment**:
   - Redis is used as the storage backend for both components
   - Supports clustering and replication for high availability

4. **Maintenance Tasks**:
   ```bash
   # Linux/macOS
   ./maintain_memory.sh
   
   # Windows
   ./maintain_memory.bat
   ```

5. **Memory Statistics**:
   ```bash
   # Linux/macOS
   ./check_memory.sh
   
   # Windows
   ./check_memory.bat
   ```

6. **Memory Tool Commands**:
   ```bash
   # Show memory statistics
   go run ./backend/cmd/tools/memory_tool/main.go -action status -path ./
   
   # Get a value from memory
   go run ./backend/cmd/tools/memory_tool/main.go -action get -key "system:user_stats" -path ./
   
   # Show knowledge graph statistics
   go run ./backend/cmd/tools/memory_tool/main.go -action graph-status -path ./
   
   # List nodes of a specific type
   go run ./backend/cmd/tools/memory_tool/main.go -action list-nodes -node-type "user" -path ./
   
   # Get related nodes
   go run ./backend/cmd/tools/memory_tool/main.go -action get-related-nodes -node-id "user-123" -path ./
   ```

### Documentation Resources

For detailed information about the memory management system, refer to these documents:

- [Memory System Implementation](./docs/memory_system_implementation.md) - Architecture and implementation details
- [Memory Tool Documentation](./docs/memory_tool_documentation.md) - Comprehensive tool usage guide
- [Knowledge Graph Usage Examples](./docs/knowledge_graph_usage_examples.md) - Common usage patterns and code examples
- [Knowledge Graph Diagrams](./docs/knowledge_graph_diagrams.md) - Visual entity relationship representations

## 🤝 Contributing

We welcome contributions to the Quester project! Here's how you can get involved:

### Development Setup

1. **Fork the repository** and clone your fork
2. **Set up the development environment** using Docker Compose
3. **Install dependencies** for both frontend and backend
4. **Create a feature branch** for your changes
5. **Follow the coding standards** outlined below

### Coding Standards

#### Frontend (Flutter)
- Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use `flutter format .` to format code
- Run `flutter analyze` to check for issues
- Write widget tests for new UI components
- Use Riverpod for state management
- Follow the established project structure

#### Backend (Go)
- Follow [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- Use `go fmt` to format code
- Run `go vet` to check for issues
- Write unit tests for new functionality
- Use GORM for database operations
- Follow the layered architecture pattern

### Pull Request Process

1. **Update documentation** if you're changing functionality
2. **Add tests** for new features or bug fixes
3. **Ensure all tests pass** (`flutter test` and `go test ./...`)
4. **Update the README** if you're adding new features
5. **Create a pull request** with a clear description of changes

### Code Review Guidelines

- **Be respectful** and constructive in reviews
- **Focus on code quality** and maintainability
- **Test thoroughly** before approving changes
- **Ask questions** if something is unclear
- **Suggest improvements** when appropriate

## 📄 License

Quester is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact

For questions, support, or feedback, please contact the team at [<EMAIL>](mailto:<EMAIL>) or open an issue on GitHub.

