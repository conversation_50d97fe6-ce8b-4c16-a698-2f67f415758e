package marketplace

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Item represents a marketplace item
type Item struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"not null" validate:"required,min=2,max=200"`
	Description string    `json:"description" gorm:"not null" validate:"required,max=1000"`
	ShortDesc   string    `json:"short_desc" validate:"max=200"`
	
	// Item properties
	CategoryID uuid.UUID `json:"category_id" gorm:"type:uuid;not null"`
	Type       string    `json:"type" gorm:"not null" validate:"oneof=avatar badge theme power_up collectible consumable"`
	Rarity     string    `json:"rarity" gorm:"default:'common'" validate:"oneof=common uncommon rare epic legendary"`
	
	// Pricing
	CoinPrice int `json:"coin_price" gorm:"default:0"`
	GemPrice  int `json:"gem_price" gorm:"default:0"`
	
	// Availability
	IsAvailable   bool       `json:"is_available" gorm:"default:true"`
	IsLimited     bool       `json:"is_limited" gorm:"default:false"`
	Stock         int        `json:"stock" gorm:"default:-1"` // -1 means unlimited
	MaxPerUser    int        `json:"max_per_user" gorm:"default:-1"` // -1 means unlimited
	AvailableFrom *time.Time `json:"available_from"`
	AvailableUntil *time.Time `json:"available_until"`
	
	// Requirements
	MinLevel        int    `json:"min_level" gorm:"default:1"`
	RequiredAchievement *uuid.UUID `json:"required_achievement,omitempty" gorm:"type:uuid"`
	RequiredQuest       *uuid.UUID `json:"required_quest,omitempty" gorm:"type:uuid"`
	
	// Display
	ImageURL    string `json:"image_url"`
	ThumbnailURL string `json:"thumbnail_url"`
	Color       string `json:"color" gorm:"default:'#007bff'" validate:"hexcolor"`
	Tags        string `json:"tags"` // Comma-separated tags
	SortOrder   int    `json:"sort_order" gorm:"default:0"`
	
	// Item effects (for power-ups and consumables)
	Effects     string `json:"effects" gorm:"type:jsonb"` // JSON data for item effects
	Duration    int    `json:"duration" gorm:"default:0"` // Duration in minutes, 0 means permanent
	
	// Relationships
	Category ItemCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// ItemCategory represents marketplace item categories
type ItemCategory struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null" validate:"required,min=2,max=50"`
	Description string    `json:"description" validate:"max=200"`
	Color       string    `json:"color" gorm:"default:'#007bff'" validate:"hexcolor"`
	IconURL     string    `json:"icon_url"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	SortOrder   int       `json:"sort_order" gorm:"default:0"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// CartItem represents items in a user's shopping cart
type CartItem struct {
	ID       uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID   uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	ItemID   uuid.UUID `json:"item_id" gorm:"type:uuid;not null"`
	Quantity int       `json:"quantity" gorm:"default:1"`
	
	// Pricing at time of adding to cart
	CoinPrice int `json:"coin_price" gorm:"default:0"`
	GemPrice  int `json:"gem_price" gorm:"default:0"`
	
	// Relationships
	Item Item `json:"item,omitempty" gorm:"foreignKey:ItemID"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Purchase represents a completed purchase transaction
type Purchase struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	
	// Purchase details
	TotalCoinPrice int    `json:"total_coin_price" gorm:"default:0"`
	TotalGemPrice  int    `json:"total_gem_price" gorm:"default:0"`
	ItemCount      int    `json:"item_count" gorm:"default:0"`
	Status         string `json:"status" gorm:"default:'completed'" validate:"oneof=pending completed failed refunded"`
	
	// Payment tracking
	PaymentMethod string `json:"payment_method" gorm:"default:'wallet'" validate:"oneof=wallet external"`
	TransactionID string `json:"transaction_id"`
	
	// Relationships
	Items []PurchaseItem `json:"items,omitempty" gorm:"foreignKey:PurchaseID;constraint:OnDelete:CASCADE"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// PurchaseItem represents individual items within a purchase
type PurchaseItem struct {
	ID         uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	PurchaseID uuid.UUID `json:"purchase_id" gorm:"type:uuid;not null"`
	ItemID     uuid.UUID `json:"item_id" gorm:"type:uuid;not null"`
	Quantity   int       `json:"quantity" gorm:"default:1"`
	
	// Pricing at time of purchase
	CoinPrice int `json:"coin_price" gorm:"default:0"`
	GemPrice  int `json:"gem_price" gorm:"default:0"`
	
	// Item details at time of purchase (for historical record)
	ItemName        string `json:"item_name" gorm:"not null"`
	ItemDescription string `json:"item_description"`
	ItemImageURL    string `json:"item_image_url"`
	ItemRarity      string `json:"item_rarity"`
	
	// Relationships
	Item Item `json:"item,omitempty" gorm:"foreignKey:ItemID"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// UserInventory represents items owned by users
type UserInventory struct {
	ID       uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID   uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	ItemID   uuid.UUID `json:"item_id" gorm:"type:uuid;not null"`
	Quantity int       `json:"quantity" gorm:"default:1"`
	
	// Usage tracking
	IsEquipped    bool       `json:"is_equipped" gorm:"default:false"`
	EquippedAt    *time.Time `json:"equipped_at"`
	LastUsedAt    *time.Time `json:"last_used_at"`
	UsageCount    int        `json:"usage_count" gorm:"default:0"`
	
	// For consumables and power-ups
	ExpiresAt     *time.Time `json:"expires_at"`
	IsActive      bool       `json:"is_active" gorm:"default:false"`
	ActivatedAt   *time.Time `json:"activated_at"`
	
	// Relationships
	Item Item `json:"item,omitempty" gorm:"foreignKey:ItemID"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Item type constants
const (
	ItemTypeAvatar      = "avatar"
	ItemTypeBadge       = "badge"
	ItemTypeTheme       = "theme"
	ItemTypePowerUp     = "power_up"
	ItemTypeCollectible = "collectible"
	ItemTypeConsumable  = "consumable"
)

// Item rarity constants
const (
	ItemRarityCommon    = "common"
	ItemRarityUncommon  = "uncommon"
	ItemRarityRare      = "rare"
	ItemRarityEpic      = "epic"
	ItemRarityLegendary = "legendary"
)

// Purchase status constants
const (
	PurchaseStatusPending   = "pending"
	PurchaseStatusCompleted = "completed"
	PurchaseStatusFailed    = "failed"
	PurchaseStatusRefunded  = "refunded"
)

// Payment method constants
const (
	PaymentMethodWallet   = "wallet"
	PaymentMethodExternal = "external"
)
