package marketplace

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	GetItems(limit, offset int) ([]Item, error)
	GetItemByID(id uuid.UUID) (*Item, error)
	GetCategories() ([]ItemCategory, error)
	GetCart(userID uuid.UUID) ([]CartItem, error)
	AddToCart(cartItem *CartItem) error
	UpdateCartItem(cartItem *CartItem) error
	RemoveFromCart(userID, itemID uuid.UUID) error
	CreatePurchase(purchase *Purchase) error
	GetInventory(userID uuid.UUID) ([]UserInventory, error)
	CreateItem(item *Item) error
	UpdateItem(item *Item) error
	DeleteItem(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetItems(limit, offset int) ([]Item, error) {
	var items []Item
	err := r.db.Preload("Category").Where("is_available = ?", true).
		Limit(limit).Offset(offset).Find(&items).Error
	return items, err
}

func (r *repository) GetItemByID(id uuid.UUID) (*Item, error) {
	var item Item
	err := r.db.Preload("Category").First(&item, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &item, nil
}

func (r *repository) GetCategories() ([]ItemCategory, error) {
	var categories []ItemCategory
	err := r.db.Where("is_active = ?", true).Order("sort_order").Find(&categories).Error
	return categories, err
}

func (r *repository) GetCart(userID uuid.UUID) ([]CartItem, error) {
	var cartItems []CartItem
	err := r.db.Preload("Item").Where("user_id = ?", userID).Find(&cartItems).Error
	return cartItems, err
}

func (r *repository) AddToCart(cartItem *CartItem) error {
	return r.db.Create(cartItem).Error
}

func (r *repository) UpdateCartItem(cartItem *CartItem) error {
	return r.db.Save(cartItem).Error
}

func (r *repository) RemoveFromCart(userID, itemID uuid.UUID) error {
	return r.db.Where("user_id = ? AND item_id = ?", userID, itemID).Delete(&CartItem{}).Error
}

func (r *repository) CreatePurchase(purchase *Purchase) error {
	return r.db.Create(purchase).Error
}

func (r *repository) GetInventory(userID uuid.UUID) ([]UserInventory, error) {
	var inventory []UserInventory
	err := r.db.Preload("Item").Where("user_id = ?", userID).Find(&inventory).Error
	return inventory, err
}

func (r *repository) CreateItem(item *Item) error {
	return r.db.Create(item).Error
}

func (r *repository) UpdateItem(item *Item) error {
	return r.db.Save(item).Error
}

func (r *repository) DeleteItem(id uuid.UUID) error {
	return r.db.Delete(&Item{}, "id = ?", id).Error
}
