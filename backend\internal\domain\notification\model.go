package notification

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Notification represents a user notification
type Notification struct {
	ID       uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID   uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	
	// Notification content
	Type     string `json:"type" gorm:"not null" validate:"oneof=quest_complete quest_available achievement_unlock friend_request level_up item_received system maintenance"`
	Title    string `json:"title" gorm:"not null" validate:"required,max=200"`
	Message  string `json:"message" gorm:"not null" validate:"required,max=1000"`
	
	// Status tracking
	IsRead      bool       `json:"is_read" gorm:"default:false"`
	ReadAt      *time.Time `json:"read_at"`
	IsDisplayed bool       `json:"is_displayed" gorm:"default:false"`
	DisplayedAt *time.Time `json:"displayed_at"`
	
	// Priority and categorization
	Priority string `json:"priority" gorm:"default:'normal'" validate:"oneof=low normal high urgent"`
	Category string `json:"category" gorm:"default:'general'" validate:"oneof=general quest achievement social system marketplace"`
	
	// Related entities
	QuestID       *uuid.UUID `json:"quest_id,omitempty" gorm:"type:uuid"`
	AchievementID *uuid.UUID `json:"achievement_id,omitempty" gorm:"type:uuid"`
	ItemID        *uuid.UUID `json:"item_id,omitempty" gorm:"type:uuid"`
	FriendID      *uuid.UUID `json:"friend_id,omitempty" gorm:"type:uuid"`
	
	// Action and navigation
	ActionType string `json:"action_type"` // What action to take when clicked
	ActionData string `json:"action_data" gorm:"type:jsonb"` // JSON data for action
	
	// Display properties
	IconURL   string `json:"icon_url"`
	ImageURL  string `json:"image_url"`
	Color     string `json:"color" gorm:"default:'#007bff'" validate:"hexcolor"`
	
	// Expiration
	ExpiresAt *time.Time `json:"expires_at"`
	
	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Notification type constants
const (
	NotificationTypeQuestComplete    = "quest_complete"
	NotificationTypeQuestAvailable   = "quest_available"
	NotificationTypeAchievementUnlock = "achievement_unlock"
	NotificationTypeFriendRequest    = "friend_request"
	NotificationTypeLevelUp          = "level_up"
	NotificationTypeItemReceived     = "item_received"
	NotificationTypeSystem           = "system"
	NotificationTypeMaintenance      = "maintenance"
)

// Notification priority constants
const (
	NotificationPriorityLow    = "low"
	NotificationPriorityNormal = "normal"
	NotificationPriorityHigh   = "high"
	NotificationPriorityUrgent = "urgent"
)

// Notification category constants
const (
	NotificationCategoryGeneral     = "general"
	NotificationCategoryQuest       = "quest"
	NotificationCategoryAchievement = "achievement"
	NotificationCategorySocial      = "social"
	NotificationCategorySystem      = "system"
	NotificationCategoryMarketplace = "marketplace"
)
