/// Responsive breakpoints for different screen sizes
/// Following Material Design 3 guidelines and Flutter best practices
class Breakpoints {
  // Mobile breakpoints
  static const double mobileSmall = 360;
  static const double mobileMedium = 390;
  static const double mobileLarge = 428;
  
  // Tablet breakpoints
  static const double tabletSmall = 600;
  static const double tabletMedium = 768;
  static const double tabletLarge = 1024;
  
  // Desktop breakpoints
  static const double desktopSmall = 1200;
  static const double desktopMedium = 1440;
  static const double desktopLarge = 1920;
  static const double desktopXLarge = 2560;
  
  // Compact width breakpoints (for responsive navigation)
  static const double compactWidth = 600;
  static const double mediumWidth = 840;
  static const double expandedWidth = 1200;
  
  // Height breakpoints for different orientations
  static const double compactHeight = 480;
  static const double mediumHeight = 900;
  
  /// Check if screen width is mobile size
  static bool isMobile(double width) => width < tabletSmall;
  
  /// Check if screen width is tablet size
  static bool isTablet(double width) => 
      width >= tabletSmall && width < desktopSmall;
  
  /// Check if screen width is desktop size
  static bool isDesktop(double width) => width >= desktopSmall;
  
  /// Check if screen has compact width (for navigation rail)
  static bool isCompactWidth(double width) => width < compactWidth;
  
  /// Check if screen has medium width
  static bool isMediumWidth(double width) => 
      width >= compactWidth && width < expandedWidth;
  
  /// Check if screen has expanded width
  static bool isExpandedWidth(double width) => width >= expandedWidth;
  
  /// Check if screen has compact height
  static bool isCompactHeight(double height) => height < compactHeight;
  
  /// Get appropriate grid columns based on screen width
  static int getGridColumns(double width) {
    if (width < tabletSmall) return 2;
    if (width < tabletLarge) return 3;
    if (width < desktopMedium) return 4;
    if (width < desktopLarge) return 5;
    return 6;
  }
  
  /// Get appropriate content padding based on screen width
  static double getContentPadding(double width) {
    if (width < tabletSmall) return 16.0;
    if (width < desktopSmall) return 24.0;
    if (width < desktopLarge) return 32.0;
    return 48.0;
  }
  
  /// Get maximum content width for readability
  static double getMaxContentWidth(double width) {
    if (width < tabletSmall) return width;
    if (width < desktopSmall) return width * 0.9;
    if (width < desktopLarge) return 1200;
    return 1440;
  }
}
