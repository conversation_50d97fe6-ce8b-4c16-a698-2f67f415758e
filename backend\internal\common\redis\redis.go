package redis

import (
	"context"
	"fmt"
	"log"
	"time"

	"quester-backend/internal/common/config"

	"github.com/go-redis/redis/v8"
)

type Client struct {
	*redis.Client
}

func Initialize(cfg config.RedisConfig) (*Client, error) {
	// Create Redis client
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	log.Println("Redis connected successfully")
	return &Client{Client: rdb}, nil
}

// Cache keys constants
const (
	UserCachePrefix         = "user:"
	QuestCachePrefix        = "quest:"
	AchievementCachePrefix  = "achievement:"
	LeaderboardCachePrefix  = "leaderboard:"
	NotificationCachePrefix = "notification:"
	SessionCachePrefix      = "session:"
)

// Helper methods for common caching operations

func (c *Client) SetUserCache(userID string, data interface{}, expiration time.Duration) error {
	key := UserCachePrefix + userID
	return c.Set(context.Background(), key, data, expiration).Err()
}

func (c *Client) GetUserCache(userID string) (string, error) {
	key := UserCachePrefix + userID
	return c.Get(context.Background(), key).Result()
}

func (c *Client) DeleteUserCache(userID string) error {
	key := UserCachePrefix + userID
	return c.Del(context.Background(), key).Err()
}

func (c *Client) SetQuestCache(questID string, data interface{}, expiration time.Duration) error {
	key := QuestCachePrefix + questID
	return c.Set(context.Background(), key, data, expiration).Err()
}

func (c *Client) GetQuestCache(questID string) (string, error) {
	key := QuestCachePrefix + questID
	return c.Get(context.Background(), key).Result()
}

func (c *Client) SetLeaderboard(leaderboardType string, userID string, score float64) error {
	key := LeaderboardCachePrefix + leaderboardType
	return c.ZAdd(context.Background(), key, &redis.Z{
		Score:  score,
		Member: userID,
	}).Err()
}

func (c *Client) GetLeaderboard(leaderboardType string, start, stop int64) ([]redis.Z, error) {
	key := LeaderboardCachePrefix + leaderboardType
	return c.ZRevRangeWithScores(context.Background(), key, start, stop).Result()
}

func (c *Client) GetUserRank(leaderboardType string, userID string) (int64, error) {
	key := LeaderboardCachePrefix + leaderboardType
	return c.ZRevRank(context.Background(), key, userID).Result()
}

func (c *Client) SetSession(sessionID string, userID string, expiration time.Duration) error {
	key := SessionCachePrefix + sessionID
	return c.Set(context.Background(), key, userID, expiration).Err()
}

func (c *Client) GetSession(sessionID string) (string, error) {
	key := SessionCachePrefix + sessionID
	return c.Get(context.Background(), key).Result()
}

func (c *Client) DeleteSession(sessionID string) error {
	key := SessionCachePrefix + sessionID
	return c.Del(context.Background(), key).Err()
}

// Pub/Sub methods for real-time notifications
func (c *Client) PublishNotification(channel string, message interface{}) error {
	return c.Publish(context.Background(), channel, message).Err()
}

func (c *Client) SubscribeToNotifications(channel string) *redis.PubSub {
	return c.Subscribe(context.Background(), channel)
}
