package user

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	// User CRUD operations
	CreateUser(user *User) error
	GetUserByID(id uuid.UUID) (*User, error)
	GetUserByEmail(email string) (*User, error)
	GetUserByUsername(username string) (*User, error)
	UpdateUser(user *User) error
	DeleteUser(id uuid.UUID) error
	ListUsers(limit, offset int) ([]User, error)
	
	// Profile operations
	CreateProfile(profile *UserProfile) error
	GetProfileByUserID(userID uuid.UUID) (*UserProfile, error)
	UpdateProfile(profile *UserProfile) error
	
	// Statistics operations
	CreateStatistics(stats *UserStatistics) error
	GetStatisticsByUserID(userID uuid.UUID) (*UserStatistics, error)
	UpdateStatistics(stats *UserStatistics) error
	IncrementQuestCompleted(userID uuid.UUID) error
	IncrementAchievementUnlocked(userID uuid.UUID) error
	UpdateLoginStreak(userID uuid.UUID) error
	
	// Activity operations
	CreateActivity(activity *UserActivity) error
	GetActivitiesByUserID(userID uuid.UUID, limit, offset int) ([]UserActivity, error)
	GetRecentActivities(limit int) ([]UserActivity, error)
	
	// Friend operations
	CreateFriendRequest(friendship *FriendRelationship) error
	GetFriendship(userID, friendID uuid.UUID) (*FriendRelationship, error)
	UpdateFriendshipStatus(id uuid.UUID, status string) error
	GetFriends(userID uuid.UUID) ([]User, error)
	GetFriendRequests(userID uuid.UUID) ([]FriendRelationship, error)
	DeleteFriendship(userID, friendID uuid.UUID) error
	
	// Wallet operations
	CreateWalletTransaction(transaction *WalletTransaction) error
	GetWalletTransactions(userID uuid.UUID, limit, offset int) ([]WalletTransaction, error)
	UpdateUserBalance(userID uuid.UUID, currency string, amount int) error
	GetUserBalance(userID uuid.UUID) (coins, gems int, err error)
	
	// Leaderboard operations
	GetTopUsersByExperience(limit int) ([]UserProfile, error)
	GetTopUsersByQuestsCompleted(limit int) ([]UserStatistics, error)
	GetUserRankByExperience(userID uuid.UUID) (int, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

// User CRUD operations
func (r *repository) CreateUser(user *User) error {
	return r.db.Create(user).Error
}

func (r *repository) GetUserByID(id uuid.UUID) (*User, error) {
	var user User
	err := r.db.Preload("Profile").Preload("Statistics").First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) GetUserByEmail(email string) (*User, error) {
	var user User
	err := r.db.Preload("Profile").Preload("Statistics").First(&user, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) GetUserByUsername(username string) (*User, error) {
	var user User
	err := r.db.Preload("Profile").Preload("Statistics").First(&user, "username = ?", username).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) UpdateUser(user *User) error {
	return r.db.Save(user).Error
}

func (r *repository) DeleteUser(id uuid.UUID) error {
	return r.db.Delete(&User{}, "id = ?", id).Error
}

func (r *repository) ListUsers(limit, offset int) ([]User, error) {
	var users []User
	err := r.db.Preload("Profile").Preload("Statistics").
		Limit(limit).Offset(offset).Find(&users).Error
	return users, err
}

// Profile operations
func (r *repository) CreateProfile(profile *UserProfile) error {
	return r.db.Create(profile).Error
}

func (r *repository) GetProfileByUserID(userID uuid.UUID) (*UserProfile, error) {
	var profile UserProfile
	err := r.db.First(&profile, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &profile, nil
}

func (r *repository) UpdateProfile(profile *UserProfile) error {
	return r.db.Save(profile).Error
}

// Statistics operations
func (r *repository) CreateStatistics(stats *UserStatistics) error {
	return r.db.Create(stats).Error
}

func (r *repository) GetStatisticsByUserID(userID uuid.UUID) (*UserStatistics, error) {
	var stats UserStatistics
	err := r.db.First(&stats, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

func (r *repository) UpdateStatistics(stats *UserStatistics) error {
	return r.db.Save(stats).Error
}

func (r *repository) IncrementQuestCompleted(userID uuid.UUID) error {
	return r.db.Model(&UserStatistics{}).
		Where("user_id = ?", userID).
		UpdateColumn("quests_completed", gorm.Expr("quests_completed + ?", 1)).Error
}

func (r *repository) IncrementAchievementUnlocked(userID uuid.UUID) error {
	return r.db.Model(&UserStatistics{}).
		Where("user_id = ?", userID).
		UpdateColumn("achievements_unlocked", gorm.Expr("achievements_unlocked + ?", 1)).Error
}

func (r *repository) UpdateLoginStreak(userID uuid.UUID) error {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	
	var stats UserStatistics
	if err := r.db.First(&stats, "user_id = ?", userID).Error; err != nil {
		return err
	}
	
	// Check if last login was yesterday
	if stats.LastLoginDate != nil {
		lastLogin := time.Date(stats.LastLoginDate.Year(), stats.LastLoginDate.Month(), 
			stats.LastLoginDate.Day(), 0, 0, 0, 0, stats.LastLoginDate.Location())
		yesterday := today.AddDate(0, 0, -1)
		
		if lastLogin.Equal(yesterday) {
			// Continue streak
			stats.CurrentLoginStreak++
		} else if !lastLogin.Equal(today) {
			// Reset streak
			stats.CurrentLoginStreak = 1
		}
	} else {
		// First login
		stats.CurrentLoginStreak = 1
	}
	
	stats.LastLoginDate = &now
	stats.TotalLoginDays++
	
	return r.db.Save(&stats).Error
}

// Activity operations
func (r *repository) CreateActivity(activity *UserActivity) error {
	return r.db.Create(activity).Error
}

func (r *repository) GetActivitiesByUserID(userID uuid.UUID, limit, offset int) ([]UserActivity, error) {
	var activities []UserActivity
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&activities).Error
	return activities, err
}

func (r *repository) GetRecentActivities(limit int) ([]UserActivity, error) {
	var activities []UserActivity
	err := r.db.Order("created_at DESC").Limit(limit).Find(&activities).Error
	return activities, err
}

// Friend operations
func (r *repository) CreateFriendRequest(friendship *FriendRelationship) error {
	return r.db.Create(friendship).Error
}

func (r *repository) GetFriendship(userID, friendID uuid.UUID) (*FriendRelationship, error) {
	var friendship FriendRelationship
	err := r.db.Where("(user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?)", 
		userID, friendID, friendID, userID).First(&friendship).Error
	if err != nil {
		return nil, err
	}
	return &friendship, nil
}

func (r *repository) UpdateFriendshipStatus(id uuid.UUID, status string) error {
	return r.db.Model(&FriendRelationship{}).
		Where("id = ?", id).
		Update("status", status).Error
}

func (r *repository) GetFriends(userID uuid.UUID) ([]User, error) {
	var users []User
	err := r.db.Table("users").
		Joins("JOIN friend_relationships ON (users.id = friend_relationships.friend_id AND friend_relationships.user_id = ?) OR (users.id = friend_relationships.user_id AND friend_relationships.friend_id = ?)", userID, userID).
		Where("friend_relationships.status = ? AND users.id != ?", FriendStatusAccepted, userID).
		Preload("Profile").
		Find(&users).Error
	return users, err
}

func (r *repository) GetFriendRequests(userID uuid.UUID) ([]FriendRelationship, error) {
	var requests []FriendRelationship
	err := r.db.Where("friend_id = ? AND status = ?", userID, FriendStatusPending).
		Preload("User").Preload("User.Profile").
		Find(&requests).Error
	return requests, err
}

func (r *repository) DeleteFriendship(userID, friendID uuid.UUID) error {
	return r.db.Where("(user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?)", 
		userID, friendID, friendID, userID).Delete(&FriendRelationship{}).Error
}

// Wallet operations
func (r *repository) CreateWalletTransaction(transaction *WalletTransaction) error {
	return r.db.Create(transaction).Error
}

func (r *repository) GetWalletTransactions(userID uuid.UUID, limit, offset int) ([]WalletTransaction, error) {
	var transactions []WalletTransaction
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&transactions).Error
	return transactions, err
}

func (r *repository) UpdateUserBalance(userID uuid.UUID, currency string, amount int) error {
	field := "coins"
	if currency == "gems" {
		field = "gems"
	}
	
	return r.db.Model(&UserProfile{}).
		Where("user_id = ?", userID).
		UpdateColumn(field, gorm.Expr(fmt.Sprintf("%s + ?", field), amount)).Error
}

func (r *repository) GetUserBalance(userID uuid.UUID) (coins, gems int, err error) {
	var profile UserProfile
	err = r.db.Select("coins, gems").First(&profile, "user_id = ?", userID).Error
	if err != nil {
		return 0, 0, err
	}
	return profile.Coins, profile.Gems, nil
}

// Leaderboard operations
func (r *repository) GetTopUsersByExperience(limit int) ([]UserProfile, error) {
	var profiles []UserProfile
	err := r.db.Where("show_on_leaderboard = ?", true).
		Order("experience DESC").
		Limit(limit).
		Find(&profiles).Error
	return profiles, err
}

func (r *repository) GetTopUsersByQuestsCompleted(limit int) ([]UserStatistics, error) {
	var stats []UserStatistics
	err := r.db.Joins("JOIN user_profiles ON user_statistics.user_id = user_profiles.user_id").
		Where("user_profiles.show_on_leaderboard = ?", true).
		Order("user_statistics.quests_completed DESC").
		Limit(limit).
		Find(&stats).Error
	return stats, err
}

func (r *repository) GetUserRankByExperience(userID uuid.UUID) (int, error) {
	var rank int64
	err := r.db.Model(&UserProfile{}).
		Where("experience > (SELECT experience FROM user_profiles WHERE user_id = ?) AND show_on_leaderboard = ?", userID, true).
		Count(&rank).Error
	return int(rank + 1), err
}
