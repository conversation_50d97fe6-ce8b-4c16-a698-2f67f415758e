package config

import (
	"os"
	"strconv"
	"time"
)

type Config struct {
	Server     ServerConfig
	Database   DatabaseConfig
	Redis      RedisConfig
	JWT        JWTConfig
	CORS       CORSConfig
	AI         AIConfig
	Blockchain BlockchainConfig
}

type ServerConfig struct {
	Host            string
	Port            string
	ReadTimeout     time.Duration
	WriteTimeout    time.Duration
	IdleTimeout     time.Duration
	ShutdownTimeout time.Duration
	Environment     string
}

type DatabaseConfig struct {
	Host         string
	Port         string
	Username     string
	Password     string
	Database     string
	Driver       string
	MaxIdleConns int
	MaxOpenConns int
	MaxLifetime  time.Duration
	LogLevel     string
	SSLMode      string
	Timezone     string
}

type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

type JWTConfig struct {
	Secret           string
	ExpirationHours  int
	RefreshTokenTTL  time.Duration
	AccessTokenTTL   time.Duration
}

type CORSConfig struct {
	AllowOrigins     string
	AllowMethods     string
	AllowHeaders     string
	AllowCredentials bool
	ExposeHeaders    string
	MaxAge           int
}

type AIConfig struct {
	ModelEndpoint string
	APIKey        string
	Enabled       bool
}

type BlockchainConfig struct {
	Enabled              bool
	Network              string
	ChainID              int
	RPC                  string
	QuestTokenAddress    string
	Difficulty           int
}

func Load() *Config {
	return &Config{
		Server: ServerConfig{
			Host:            getEnv("BACKEND_HOST", "0.0.0.0"),
			Port:            getEnv("BACKEND_PORT", "8080"),
			ReadTimeout:     parseDuration(getEnv("SERVER_READ_TIMEOUT", "10s")),
			WriteTimeout:    parseDuration(getEnv("SERVER_WRITE_TIMEOUT", "10s")),
			IdleTimeout:     parseDuration(getEnv("SERVER_IDLE_TIMEOUT", "120s")),
			ShutdownTimeout: parseDuration(getEnv("SERVER_SHUTDOWN_TIMEOUT", "20s")),
			Environment:     getEnv("GO_ENV", "development"),
		},
		Database: DatabaseConfig{
			Host:         getEnv("DATABASE_HOST", "localhost"),
			Port:         getEnv("DATABASE_PORT", "5432"),
			Username:     getEnv("DATABASE_USERNAME", "keshabalive"),
			Password:     getEnv("DATABASE_PASSWORD", "9871"),
			Database:     getEnv("DATABASE_DATABASE", "quester"),
			Driver:       getEnv("DATABASE_DRIVER", "postgres"),
			MaxIdleConns: parseInt(getEnv("DATABASE_MAX_IDLE_CONNS", "10")),
			MaxOpenConns: parseInt(getEnv("DATABASE_MAX_OPEN_CONNS", "100")),
			MaxLifetime:  parseDuration(getEnv("DATABASE_MAX_LIFETIME", "1h")),
			LogLevel:     getEnv("DATABASE_LOG_LEVEL", "info"),
			SSLMode:      getEnv("DATABASE_SSL_MODE", "disable"),
			Timezone:     getEnv("DATABASE_TIMEZONE", "UTC"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       parseInt(getEnv("REDIS_DB", "0")),
		},
		JWT: JWTConfig{
			Secret:          getEnv("JWT_SECRET", "your-secret-key-here"),
			ExpirationHours: parseInt(getEnv("AUTH_JWT_EXPIRATION_HOURS", "24")),
			RefreshTokenTTL: parseDuration(getEnv("JWT_REFRESH_TTL", "168h")), // 7 days
			AccessTokenTTL:  parseDuration(getEnv("JWT_ACCESS_TTL", "24h")),   // 1 day
		},
		CORS: CORSConfig{
			AllowOrigins:     getEnv("CORS_ALLOW_ORIGINS", "*"),
			AllowMethods:     getEnv("CORS_ALLOW_METHODS", "GET,POST,PUT,DELETE,OPTIONS"),
			AllowHeaders:     getEnv("CORS_ALLOW_HEADERS", "Origin,Content-Type,Accept,Authorization"),
			AllowCredentials: parseBool(getEnv("CORS_ALLOW_CREDENTIALS", "true")),
			ExposeHeaders:    getEnv("CORS_EXPOSE_HEADERS", ""),
			MaxAge:           parseInt(getEnv("CORS_MAX_AGE", "86400")),
		},
		AI: AIConfig{
			ModelEndpoint: getEnv("AI_MODEL_ENDPOINT", ""),
			APIKey:        getEnv("AI_API_KEY", ""),
			Enabled:       parseBool(getEnv("ENABLE_AI_FEATURES", "false")),
		},
		Blockchain: BlockchainConfig{
			Enabled:           parseBool(getEnv("BLOCKCHAIN_ENABLED", "false")),
			Network:           getEnv("BLOCKCHAIN_NETWORK", "testnet"),
			ChainID:           parseInt(getEnv("BLOCKCHAIN_CHAIN_ID", "1")),
			RPC:               getEnv("BLOCKCHAIN_RPC", ""),
			QuestTokenAddress: getEnv("BLOCKCHAIN_QUEST_TOKEN_ADDRESS", ""),
			Difficulty:        parseInt(getEnv("BLOCKCHAIN_DIFFICULTY", "4")),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func parseInt(s string) int {
	if i, err := strconv.Atoi(s); err == nil {
		return i
	}
	return 0
}

func parseBool(s string) bool {
	if b, err := strconv.ParseBool(s); err == nil {
		return b
	}
	return false
}

func parseDuration(s string) time.Duration {
	if d, err := time.ParseDuration(s); err == nil {
		return d
	}
	return 0
}
