package quest

import (
	"fmt"
	"time"

	"quester-backend/internal/common/redis"
	"quester-backend/internal/domain/user"

	"github.com/google/uuid"
)

type Service interface {
	GetQuests(limit, offset int) ([]Quest, error)
	GetQuest(id uuid.UUID) (*Quest, error)
	GetCategories() ([]QuestCategory, error)
	GetRecommendedQuests(userID uuid.UUID, limit int) ([]Quest, error)
	StartQuest(userID, questID uuid.UUID) error
	UpdateProgress(userID, questID uuid.UUID, stepID uuid.UUID, progress int) error
	CompleteQuest(userID, questID uuid.UUID) error
	ClaimReward(userID, questID uuid.UUID) error
	GetUserActiveQuests(userID uuid.UUID) ([]UserQuest, error)
	GetUserCompletedQuests(userID uuid.UUID, limit, offset int) ([]UserQuest, error)
	
	// Admin methods
	CreateQuest(quest *Quest) error
	UpdateQuest(quest *Quest) error
	DeleteQuest(id uuid.UUID) error
}

type service struct {
	repo     Repository
	userRepo user.Repository
	redis    *redis.Client
}

func NewService(repo Repository, userRepo user.Repository, redisClient *redis.Client) Service {
	return &service{
		repo:     repo,
		userRepo: userRepo,
		redis:    redisClient,
	}
}

func (s *service) GetQuests(limit, offset int) ([]Quest, error) {
	return s.repo.ListQuests(limit, offset)
}

func (s *service) GetQuest(id uuid.UUID) (*Quest, error) {
	return s.repo.GetQuestByID(id)
}

func (s *service) GetCategories() ([]QuestCategory, error) {
	return s.repo.GetCategories()
}

func (s *service) GetRecommendedQuests(userID uuid.UUID, limit int) ([]Quest, error) {
	// TODO: Implement AI-based recommendations
	// For now, return recent quests
	return s.repo.ListQuests(limit, 0)
}

func (s *service) StartQuest(userID, questID uuid.UUID) error {
	// Check if quest exists
	quest, err := s.repo.GetQuestByID(questID)
	if err != nil {
		return fmt.Errorf("quest not found: %w", err)
	}

	// Check if user already has this quest
	if _, err := s.repo.GetUserQuest(userID, questID); err == nil {
		return fmt.Errorf("quest already started")
	}

	// Check user level requirements
	userProfile, err := s.userRepo.GetProfileByUserID(userID)
	if err != nil {
		return fmt.Errorf("failed to get user profile: %w", err)
	}

	if userProfile.Level < quest.MinLevel {
		return fmt.Errorf("user level too low (required: %d, current: %d)", quest.MinLevel, userProfile.Level)
	}

	// Create user quest
	userQuest := &UserQuest{
		UserID:    userID,
		QuestID:   questID,
		Status:    QuestStatusInProgress,
		Progress:  0,
		StartedAt: &time.Time{},
	}
	*userQuest.StartedAt = time.Now()

	if err := s.repo.StartQuest(userQuest); err != nil {
		return fmt.Errorf("failed to start quest: %w", err)
	}

	// Create activity record
	activity := &user.UserActivity{
		UserID:      userID,
		Type:        user.ActivityTypeQuestStart,
		Description: fmt.Sprintf("Started quest: %s", quest.Title),
		QuestID:     &questID,
	}

	if err := s.userRepo.CreateActivity(activity); err != nil {
		// Log error but don't fail the quest start
		fmt.Printf("Failed to create activity: %v\n", err)
	}

	return nil
}

func (s *service) UpdateProgress(userID, questID uuid.UUID, stepID uuid.UUID, progress int) error {
	// Get user quest
	userQuest, err := s.repo.GetUserQuest(userID, questID)
	if err != nil {
		return fmt.Errorf("user quest not found: %w", err)
	}

	if userQuest.Status != QuestStatusInProgress {
		return fmt.Errorf("quest is not in progress")
	}

	// TODO: Implement step progress tracking
	// For now, just update overall progress
	userQuest.Progress = progress
	
	return s.repo.UpdateUserQuest(userQuest)
}

func (s *service) CompleteQuest(userID, questID uuid.UUID) error {
	// Get user quest
	userQuest, err := s.repo.GetUserQuest(userID, questID)
	if err != nil {
		return fmt.Errorf("user quest not found: %w", err)
	}

	if userQuest.Status != QuestStatusInProgress {
		return fmt.Errorf("quest is not in progress")
	}

	// Mark as completed
	now := time.Now()
	userQuest.Status = QuestStatusCompleted
	userQuest.Progress = 100
	userQuest.CompletedAt = &now

	if err := s.repo.UpdateUserQuest(userQuest); err != nil {
		return fmt.Errorf("failed to update quest: %w", err)
	}

	// Update user statistics
	if err := s.userRepo.IncrementQuestCompleted(userID); err != nil {
		fmt.Printf("Failed to update quest statistics: %v\n", err)
	}

	// Create activity record
	activity := &user.UserActivity{
		UserID:      userID,
		Type:        user.ActivityTypeQuestComplete,
		Description: fmt.Sprintf("Completed quest: %s", userQuest.Quest.Title),
		Points:      userQuest.Quest.ExperienceReward,
		QuestID:     &questID,
	}

	if err := s.userRepo.CreateActivity(activity); err != nil {
		fmt.Printf("Failed to create activity: %v\n", err)
	}

	return nil
}

func (s *service) ClaimReward(userID, questID uuid.UUID) error {
	// Get user quest
	userQuest, err := s.repo.GetUserQuest(userID, questID)
	if err != nil {
		return fmt.Errorf("user quest not found: %w", err)
	}

	if userQuest.Status != QuestStatusCompleted {
		return fmt.Errorf("quest is not completed")
	}

	if userQuest.RewardsClaimed {
		return fmt.Errorf("rewards already claimed")
	}

	// Get quest details
	quest, err := s.repo.GetQuestByID(questID)
	if err != nil {
		return fmt.Errorf("quest not found: %w", err)
	}

	// Award experience
	if quest.ExperienceReward > 0 {
		// TODO: Implement experience system
		fmt.Printf("Awarding %d experience to user %s\n", quest.ExperienceReward, userID)
	}

	// Award coins
	if quest.CoinsReward > 0 {
		description := fmt.Sprintf("Quest reward: %s", quest.Title)
		if err := s.userRepo.CreateWalletTransaction(&user.WalletTransaction{
			UserID:      userID,
			Type:        "earn",
			Currency:    "coins",
			Amount:      quest.CoinsReward,
			Description: description,
			Reference:   questID.String(),
		}); err != nil {
			return fmt.Errorf("failed to award coins: %w", err)
		}

		// Update user balance
		if err := s.userRepo.UpdateUserBalance(userID, "coins", quest.CoinsReward); err != nil {
			return fmt.Errorf("failed to update coin balance: %w", err)
		}
	}

	// Award gems
	if quest.GemsReward > 0 {
		description := fmt.Sprintf("Quest reward: %s", quest.Title)
		if err := s.userRepo.CreateWalletTransaction(&user.WalletTransaction{
			UserID:      userID,
			Type:        "earn",
			Currency:    "gems",
			Amount:      quest.GemsReward,
			Description: description,
			Reference:   questID.String(),
		}); err != nil {
			return fmt.Errorf("failed to award gems: %w", err)
		}

		// Update user balance
		if err := s.userRepo.UpdateUserBalance(userID, "gems", quest.GemsReward); err != nil {
			return fmt.Errorf("failed to update gem balance: %w", err)
		}
	}

	// Mark rewards as claimed
	now := time.Now()
	userQuest.RewardsClaimed = true
	userQuest.ClaimedAt = &now

	if err := s.repo.UpdateUserQuest(userQuest); err != nil {
		return fmt.Errorf("failed to update quest: %w", err)
	}

	return nil
}

func (s *service) GetUserActiveQuests(userID uuid.UUID) ([]UserQuest, error) {
	return s.repo.GetUserActiveQuests(userID)
}

func (s *service) GetUserCompletedQuests(userID uuid.UUID, limit, offset int) ([]UserQuest, error) {
	return s.repo.GetUserCompletedQuests(userID, limit, offset)
}

// Admin methods
func (s *service) CreateQuest(quest *Quest) error {
	return s.repo.CreateQuest(quest)
}

func (s *service) UpdateQuest(quest *Quest) error {
	return s.repo.UpdateQuest(quest)
}

func (s *service) DeleteQuest(id uuid.UUID) error {
	return s.repo.DeleteQuest(id)
}
