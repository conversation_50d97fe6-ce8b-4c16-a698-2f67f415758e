package quest

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Quest represents a quest that users can undertake
type Quest struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Title       string    `json:"title" gorm:"not null" validate:"required,min=3,max=200"`
	Description string    `json:"description" gorm:"not null" validate:"required,max=1000"`
	ShortDesc   string    `json:"short_desc" validate:"max=200"`

	// Quest properties
	CategoryID uuid.UUID `json:"category_id" gorm:"type:uuid;not null"`
	Type       string    `json:"type" gorm:"not null" validate:"oneof=daily weekly monthly one_time chain"`
	Difficulty string    `json:"difficulty" gorm:"default:'easy'" validate:"oneof=easy medium hard expert"`
	Priority   int       `json:"priority" gorm:"default:0"`

	// Requirements and constraints
	MinLevel      int  `json:"min_level" gorm:"default:1"`
	MaxLevel      int  `json:"max_level" gorm:"default:0"` // 0 means no limit
	IsRepeatable  bool `json:"is_repeatable" gorm:"default:false"`
	CooldownHours int  `json:"cooldown_hours" gorm:"default:0"`

	// Timing
	Duration  int        `json:"duration" gorm:"default:0"` // in minutes, 0 means no limit
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`

	// Rewards
	ExperienceReward int `json:"experience_reward" gorm:"default:0"`
	CoinsReward      int `json:"coins_reward" gorm:"default:0"`
	GemsReward       int `json:"gems_reward" gorm:"default:0"`

	// Status and metadata
	IsActive    bool   `json:"is_active" gorm:"default:true"`
	IsPublished bool   `json:"is_published" gorm:"default:false"`
	Tags        string `json:"tags"` // Comma-separated tags
	ImageURL    string `json:"image_url"`
	IconURL     string `json:"icon_url"`

	// Relationships
	Category QuestCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Steps    []QuestStep   `json:"steps,omitempty" gorm:"foreignKey:QuestID;constraint:OnDelete:CASCADE"`
	Rewards  []QuestReward `json:"rewards,omitempty" gorm:"foreignKey:QuestID;constraint:OnDelete:CASCADE"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// QuestCategory represents quest categories
type QuestCategory struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null" validate:"required,min=2,max=50"`
	Description string    `json:"description" validate:"max=200"`
	Color       string    `json:"color" gorm:"default:'#007bff'" validate:"hexcolor"`
	IconURL     string    `json:"icon_url"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	SortOrder   int       `json:"sort_order" gorm:"default:0"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// QuestStep represents individual steps within a quest
type QuestStep struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	QuestID     uuid.UUID `json:"quest_id" gorm:"type:uuid;not null"`
	Title       string    `json:"title" gorm:"not null" validate:"required,min=3,max=200"`
	Description string    `json:"description" validate:"max=500"`
	StepOrder   int       `json:"step_order" gorm:"not null"`

	// Step requirements
	Type        string `json:"type" gorm:"not null" validate:"oneof=action visit collect interact time_based custom"`
	Target      string `json:"target"` // What needs to be done
	TargetCount int    `json:"target_count" gorm:"default:1"`
	IsOptional  bool   `json:"is_optional" gorm:"default:false"`

	// Validation
	ValidationRule string `json:"validation_rule"` // JSON rule for validation
	AutoComplete   bool   `json:"auto_complete" gorm:"default:false"`

	// Rewards for completing this step
	ExperienceReward int `json:"experience_reward" gorm:"default:0"`
	CoinsReward      int `json:"coins_reward" gorm:"default:0"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// UserQuest represents a user's participation in a quest
type UserQuest struct {
	ID      uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID  uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	QuestID uuid.UUID `json:"quest_id" gorm:"type:uuid;not null;index"`

	// Status tracking
	Status      string     `json:"status" gorm:"default:'not_started'" validate:"oneof=not_started in_progress completed failed abandoned"`
	Progress    int        `json:"progress" gorm:"default:0"` // Percentage 0-100
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	FailedAt    *time.Time `json:"failed_at"`

	// Rewards tracking
	RewardsClaimed bool       `json:"rewards_claimed" gorm:"default:false"`
	ClaimedAt      *time.Time `json:"claimed_at"`

	// Attempt tracking
	AttemptCount  int        `json:"attempt_count" gorm:"default:0"`
	LastAttemptAt *time.Time `json:"last_attempt_at"`

	// Relationships
	Quest           Quest           `json:"quest,omitempty" gorm:"foreignKey:QuestID"`
	ProgressDetails []QuestProgress `json:"progress_details,omitempty" gorm:"foreignKey:UserQuestID;constraint:OnDelete:CASCADE"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// QuestProgress tracks progress on individual quest steps
type QuestProgress struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserQuestID uuid.UUID `json:"user_quest_id" gorm:"type:uuid;not null"`
	StepID      uuid.UUID `json:"step_id" gorm:"type:uuid;not null"`

	// Progress tracking
	Status        string     `json:"status" gorm:"default:'not_started'" validate:"oneof=not_started in_progress completed skipped"`
	CurrentCount  int        `json:"current_count" gorm:"default:0"`
	RequiredCount int        `json:"required_count" gorm:"default:1"`
	CompletedAt   *time.Time `json:"completed_at"`

	// Data storage for step validation
	Data string `json:"data" gorm:"type:jsonb"` // JSON data for step-specific information

	// Relationships
	Step QuestStep `json:"step,omitempty" gorm:"foreignKey:StepID"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// QuestReward represents additional rewards for quest completion
type QuestReward struct {
	ID      uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	QuestID uuid.UUID `json:"quest_id" gorm:"type:uuid;not null"`

	// Reward details
	Type        string     `json:"type" gorm:"not null" validate:"oneof=item badge achievement title"`
	ItemID      *uuid.UUID `json:"item_id,omitempty" gorm:"type:uuid"`
	Quantity    int        `json:"quantity" gorm:"default:1"`
	Name        string     `json:"name" gorm:"not null"`
	Description string     `json:"description"`
	ImageURL    string     `json:"image_url"`

	// Rarity and conditions
	Rarity     string  `json:"rarity" gorm:"default:'common'" validate:"oneof=common uncommon rare epic legendary"`
	DropChance float64 `json:"drop_chance" gorm:"default:100.0"` // Percentage 0-100

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Quest type constants
const (
	QuestTypeDaily   = "daily"
	QuestTypeWeekly  = "weekly"
	QuestTypeMonthly = "monthly"
	QuestTypeOneTime = "one_time"
	QuestTypeChain   = "chain"
)

// Quest difficulty constants
const (
	QuestDifficultyEasy   = "easy"
	QuestDifficultyMedium = "medium"
	QuestDifficultyHard   = "hard"
	QuestDifficultyExpert = "expert"
)

// Quest status constants
const (
	QuestStatusNotStarted = "not_started"
	QuestStatusInProgress = "in_progress"
	QuestStatusCompleted  = "completed"
	QuestStatusFailed     = "failed"
	QuestStatusAbandoned  = "abandoned"
)

// Quest step type constants
const (
	StepTypeAction    = "action"
	StepTypeVisit     = "visit"
	StepTypeCollect   = "collect"
	StepTypeInteract  = "interact"
	StepTypeTimeBased = "time_based"
	StepTypeCustom    = "custom"
)

// Quest step status constants
const (
	StepStatusNotStarted = "not_started"
	StepStatusInProgress = "in_progress"
	StepStatusCompleted  = "completed"
	StepStatusSkipped    = "skipped"
)

// Reward type constants
const (
	RewardTypeItem        = "item"
	RewardTypeBadge       = "badge"
	RewardTypeAchievement = "achievement"
	RewardTypeTitle       = "title"
)

// Rarity constants
const (
	RarityCommon    = "common"
	RarityUncommon  = "uncommon"
	RarityRare      = "rare"
	RarityEpic      = "epic"
	RarityLegendary = "legendary"
)
