
# Frontend Dockerfile for Flutter Web Application
# Uses Flutter SDK for web deployment

# Development stage
FROM ubuntu:22.04 AS development

# Set build arguments
ARG FLUTTER_ENV=development
ARG FLUTTER_WEB_PORT=8080

# Set environment variables
ENV FLUTTER_ENV=$FLUTTER_ENV \
    FLUTTER_WEB_PORT=$FLUTTER_WEB_PORT \
    FLUTTER_HOME="/opt/flutter" \
    PATH="/opt/flutter/bin:${PATH}"

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    dos2unix \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Flutter
RUN git clone https://github.com/flutter/flutter.git -b stable /opt/flutter && \
    flutter doctor && \
    flutter config --enable-web

# Copy pubspec files first for better caching
COPY frontend/pubspec.yaml ./
COPY frontend/pubspec.lock ./

# Copy environment files and fix line endings
COPY frontend/.env* ./
RUN if [ -f .env ]; then dos2unix .env; fi && \
    if [ -f .env ]; then sed -i 's/\r$//' .env; fi && \
    if [ -f .env.production ]; then dos2unix .env.production; fi && \
    if [ -f .env.production ]; then sed -i 's/\r$//' .env.production; fi

# Get Flutter dependencies
RUN flutter pub get

# Copy source code
COPY frontend/ ./

# Create the start script in a safe location
RUN printf '#!/bin/bash\nset -e\necho "Starting Flutter development server..."\necho "Working directory: $(pwd)"\necho "Flutter version: $(flutter --version)"\necho "Port: $FLUTTER_WEB_PORT"\nflutter pub get\necho "Starting Flutter web server on port $FLUTTER_WEB_PORT..."\nflutter run -d web-server --web-port=$FLUTTER_WEB_PORT --web-hostname=0.0.0.0 --hot\n' > /usr/local/bin/start-flutter.sh && \
    chmod +x /usr/local/bin/start-flutter.sh && \
    ls -la /usr/local/bin/start-flutter.sh && \
    cat /usr/local/bin/start-flutter.sh

# Expose port for Flutter web server
EXPOSE $FLUTTER_WEB_PORT

# Start Flutter development server
CMD ["/usr/local/bin/start-flutter.sh"]

# Build stage
FROM ubuntu:22.04 AS builder

# Set build arguments
ARG BUILD_ENV=production

# Set environment variables
ENV FLUTTER_HOME="/opt/flutter" \
    PATH="/opt/flutter/bin:${PATH}"

WORKDIR /app

# Install system dependencies and Flutter
RUN apt-get update && apt-get install -y \
    git \
    curl \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    dos2unix \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* && \
    git clone https://github.com/flutter/flutter.git -b stable /opt/flutter && \
    flutter doctor && \
    flutter config --enable-web

# Copy pubspec files first for better caching
COPY frontend/pubspec.yaml ./
COPY frontend/pubspec.lock ./

# Get dependencies
RUN flutter pub get

# Copy the rest of the frontend source
COPY frontend/ ./

# Handle Windows line endings in .env files if they exist
RUN if [ -f .env ]; then sed -i 's/\r$//' .env; fi && \
    if [ -f .env.production ]; then sed -i 's/\r$//' .env.production; fi

# Use appropriate environment file based on build environment
RUN if [ "$BUILD_ENV" = "production" ] && [ -f .env.production ]; then \
        cp .env.production .env; \
    fi

# Build the Flutter web app
RUN flutter build web --release --web-renderer html

# Production stage
FROM nginx:stable-alpine AS production

# Copy the built app from the build stage
COPY --from=builder /app/build/web /usr/share/nginx/html

# Copy default nginx configuration
COPY settings/nginx/default.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

