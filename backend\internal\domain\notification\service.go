package notification

import (
	"quester-backend/internal/common/redis"

	"github.com/google/uuid"
)

type Service interface {
	GetNotifications(userID uuid.UUID, limit, offset int) ([]Notification, error)
	GetNotificationByID(id uuid.UUID) (*Notification, error)
	GetUnreadCount(userID uuid.UUID) (int, error)
	CreateNotification(notification *Notification) error
	MarkAsRead(id uuid.UUID) error
	MarkAllAsRead(userID uuid.UUID) error
	DeleteNotification(id uuid.UUID) error
}

type service struct {
	repo  Repository
	redis *redis.Client
}

func NewService(repo Repository, redisClient *redis.Client) Service {
	return &service{
		repo:  repo,
		redis: redisClient,
	}
}

func (s *service) GetNotifications(userID uuid.UUID, limit, offset int) ([]Notification, error) {
	return s.repo.GetNotifications(userID, limit, offset)
}

func (s *service) GetNotificationByID(id uuid.UUID) (*Notification, error) {
	return s.repo.GetNotificationByID(id)
}

func (s *service) GetUnreadCount(userID uuid.UUID) (int, error) {
	return s.repo.GetUnreadCount(userID)
}

func (s *service) CreateNotification(notification *Notification) error {
	if err := s.repo.CreateNotification(notification); err != nil {
		return err
	}

	// Send real-time notification via WebSocket
	channel := "notifications:" + notification.UserID.String()
	if err := s.redis.PublishNotification(channel, notification); err != nil {
		// Log error but don't fail the notification creation
		// log.Printf("Failed to publish notification: %v", err)
	}

	return nil
}

func (s *service) MarkAsRead(id uuid.UUID) error {
	return s.repo.MarkAsRead(id)
}

func (s *service) MarkAllAsRead(userID uuid.UUID) error {
	return s.repo.MarkAllAsRead(userID)
}

func (s *service) DeleteNotification(id uuid.UUID) error {
	return s.repo.DeleteNotification(id)
}
