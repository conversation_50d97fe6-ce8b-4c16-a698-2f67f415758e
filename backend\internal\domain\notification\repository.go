package notification

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	GetNotifications(userID uuid.UUID, limit, offset int) ([]Notification, error)
	GetNotificationByID(id uuid.UUID) (*Notification, error)
	GetUnreadCount(userID uuid.UUID) (int, error)
	CreateNotification(notification *Notification) error
	MarkAsRead(id uuid.UUID) error
	MarkAllAsRead(userID uuid.UUID) error
	DeleteNotification(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) GetNotifications(userID uuid.UUID, limit, offset int) ([]Notification, error) {
	var notifications []Notification
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&notifications).Error
	return notifications, err
}

func (r *repository) GetNotificationByID(id uuid.UUID) (*Notification, error) {
	var notification Notification
	err := r.db.First(&notification, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

func (r *repository) GetUnreadCount(userID uuid.UUID) (int, error) {
	var count int64
	err := r.db.Model(&Notification{}).
		Where("user_id = ? AND is_read = ?", userID, false).
		Count(&count).Error
	return int(count), err
}

func (r *repository) CreateNotification(notification *Notification) error {
	return r.db.Create(notification).Error
}

func (r *repository) MarkAsRead(id uuid.UUID) error {
	return r.db.Model(&Notification{}).
		Where("id = ?", id).
		Update("is_read", true).Error
}

func (r *repository) MarkAllAsRead(userID uuid.UUID) error {
	return r.db.Model(&Notification{}).
		Where("user_id = ?", userID).
		Update("is_read", true).Error
}

func (r *repository) DeleteNotification(id uuid.UUID) error {
	return r.db.Delete(&Notification{}, "id = ?", id).Error
}
