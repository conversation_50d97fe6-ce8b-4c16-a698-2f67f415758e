import 'package:flutter/material.dart';
import 'responsive_builder.dart';
import 'breakpoints.dart';

/// Responsive layout widget that adapts to different screen sizes
class ResponsiveLayout extends StatelessWidget {
  final Widget? mobileBody;
  final Widget? tabletBody;
  final Widget? desktopBody;
  final Widget? drawer;
  final Widget? endDrawer;
  final PreferredSizeWidget? appBar;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final List<Widget>? persistentFooterButtons;
  final Widget? bottomNavigationBar;
  final Widget? bottomSheet;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final bool extendBody;
  final bool extendBodyBehindAppBar;

  const ResponsiveLayout({
    super.key,
    this.mobileBody,
    this.tabletBody,
    this.desktopBody,
    this.drawer,
    this.endDrawer,
    this.appBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.persistentFooterButtons,
    this.bottomNavigationBar,
    this.bottomSheet,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenInfo) {
        Widget? body;
        
        if (screenInfo.isMobile && mobileBody != null) {
          body = mobileBody;
        } else if (screenInfo.isTablet && tabletBody != null) {
          body = tabletBody;
        } else if (screenInfo.isDesktop && desktopBody != null) {
          body = desktopBody;
        } else {
          // Fallback logic
          body = desktopBody ?? tabletBody ?? mobileBody;
        }

        return Scaffold(
          appBar: appBar,
          body: body,
          drawer: screenInfo.isMobile ? drawer : null,
          endDrawer: endDrawer,
          floatingActionButton: floatingActionButton,
          floatingActionButtonLocation: floatingActionButtonLocation,
          persistentFooterButtons: persistentFooterButtons,
          bottomNavigationBar: screenInfo.isMobile ? bottomNavigationBar : null,
          bottomSheet: bottomSheet,
          backgroundColor: backgroundColor,
          resizeToAvoidBottomInset: resizeToAvoidBottomInset,
          extendBody: extendBody,
          extendBodyBehindAppBar: extendBodyBehindAppBar,
        );
      },
    );
  }
}

/// Adaptive container that adjusts its constraints based on screen size
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? maxWidth;
  final bool centerContent;
  final CrossAxisAlignment crossAxisAlignment;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.maxWidth,
    this.centerContent = true,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenInfo) {
        final effectivePadding = padding ?? 
            EdgeInsets.all(screenInfo.contentPadding);
        
        final effectiveMaxWidth = maxWidth ?? screenInfo.maxContentWidth;

        Widget content = Container(
          padding: effectivePadding,
          margin: margin,
          constraints: BoxConstraints(maxWidth: effectiveMaxWidth),
          child: child,
        );

        if (centerContent && screenInfo.isDesktop) {
          content = Center(child: content);
        }

        return content;
      },
    );
  }
}

/// Responsive grid that adapts column count based on screen size
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final EdgeInsetsGeometry? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenInfo) {
        int columns;
        
        if (screenInfo.isMobile && mobileColumns != null) {
          columns = mobileColumns!;
        } else if (screenInfo.isTablet && tabletColumns != null) {
          columns = tabletColumns!;
        } else if (screenInfo.isDesktop && desktopColumns != null) {
          columns = desktopColumns!;
        } else {
          columns = screenInfo.gridColumns;
        }

        return Padding(
          padding: padding ?? EdgeInsets.all(screenInfo.contentPadding),
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columns,
              crossAxisSpacing: spacing,
              mainAxisSpacing: runSpacing,
              childAspectRatio: 1.0,
            ),
            itemCount: children.length,
            itemBuilder: (context, index) => children[index],
          ),
        );
      },
    );
  }
}

/// Responsive wrap that adjusts spacing based on screen size
class ResponsiveWrap extends StatelessWidget {
  final List<Widget> children;
  final Axis direction;
  final WrapAlignment alignment;
  final WrapCrossAlignment crossAxisAlignment;
  final double? spacing;
  final double? runSpacing;

  const ResponsiveWrap({
    super.key,
    required this.children,
    this.direction = Axis.horizontal,
    this.alignment = WrapAlignment.start,
    this.crossAxisAlignment = WrapCrossAlignment.start,
    this.spacing,
    this.runSpacing,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenInfo) {
        final effectiveSpacing = spacing ?? 
            (screenInfo.isMobile ? 8.0 : 16.0);
        final effectiveRunSpacing = runSpacing ?? 
            (screenInfo.isMobile ? 8.0 : 16.0);

        return Wrap(
          direction: direction,
          alignment: alignment,
          crossAxisAlignment: crossAxisAlignment,
          spacing: effectiveSpacing,
          runSpacing: effectiveRunSpacing,
          children: children,
        );
      },
    );
  }
}

/// Responsive padding that adjusts based on screen size
class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? mobile;
  final EdgeInsetsGeometry? tablet;
  final EdgeInsetsGeometry? desktop;

  const ResponsivePadding({
    super.key,
    required this.child,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenInfo) {
        EdgeInsetsGeometry padding;
        
        if (screenInfo.isMobile && mobile != null) {
          padding = mobile!;
        } else if (screenInfo.isTablet && tablet != null) {
          padding = tablet!;
        } else if (screenInfo.isDesktop && desktop != null) {
          padding = desktop!;
        } else {
          padding = EdgeInsets.all(screenInfo.contentPadding);
        }

        return Padding(
          padding: padding,
          child: child,
        );
      },
    );
  }
}

/// Responsive sized box that adjusts dimensions based on screen size
class ResponsiveSizedBox extends StatelessWidget {
  final Widget? child;
  final double? mobileWidth;
  final double? mobileHeight;
  final double? tabletWidth;
  final double? tabletHeight;
  final double? desktopWidth;
  final double? desktopHeight;

  const ResponsiveSizedBox({
    super.key,
    this.child,
    this.mobileWidth,
    this.mobileHeight,
    this.tabletWidth,
    this.tabletHeight,
    this.desktopWidth,
    this.desktopHeight,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenInfo) {
        double? width;
        double? height;
        
        if (screenInfo.isMobile) {
          width = mobileWidth;
          height = mobileHeight;
        } else if (screenInfo.isTablet) {
          width = tabletWidth ?? mobileWidth;
          height = tabletHeight ?? mobileHeight;
        } else {
          width = desktopWidth ?? tabletWidth ?? mobileWidth;
          height = desktopHeight ?? tabletHeight ?? mobileHeight;
        }

        return SizedBox(
          width: width,
          height: height,
          child: child,
        );
      },
    );
  }
}
