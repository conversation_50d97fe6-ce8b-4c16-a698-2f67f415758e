package quest

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	// Quest CRUD operations
	CreateQuest(quest *Quest) error
	GetQuestByID(id uuid.UUID) (*Quest, error)
	UpdateQuest(quest *Quest) error
	DeleteQuest(id uuid.UUID) error
	ListQuests(limit, offset int) ([]Quest, error)
	
	// Category operations
	GetCategories() ([]QuestCategory, error)
	GetQuestsByCategory(categoryID uuid.UUID, limit, offset int) ([]Quest, error)
	
	// User quest operations
	StartQuest(userQuest *UserQuest) error
	GetUserQuest(userID, questID uuid.UUID) (*UserQuest, error)
	UpdateUserQuest(userQuest *UserQuest) error
	GetUserActiveQuests(userID uuid.UUID) ([]UserQuest, error)
	GetUserCompletedQuests(userID uuid.UUID, limit, offset int) ([]UserQuest, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateQuest(quest *Quest) error {
	return r.db.Create(quest).Error
}

func (r *repository) GetQuestByID(id uuid.UUID) (*Quest, error) {
	var quest Quest
	err := r.db.Preload("Category").Preload("Steps").Preload("Rewards").First(&quest, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &quest, nil
}

func (r *repository) UpdateQuest(quest *Quest) error {
	return r.db.Save(quest).Error
}

func (r *repository) DeleteQuest(id uuid.UUID) error {
	return r.db.Delete(&Quest{}, "id = ?", id).Error
}

func (r *repository) ListQuests(limit, offset int) ([]Quest, error) {
	var quests []Quest
	err := r.db.Preload("Category").Limit(limit).Offset(offset).Find(&quests).Error
	return quests, err
}

func (r *repository) GetCategories() ([]QuestCategory, error) {
	var categories []QuestCategory
	err := r.db.Where("is_active = ?", true).Order("sort_order").Find(&categories).Error
	return categories, err
}

func (r *repository) GetQuestsByCategory(categoryID uuid.UUID, limit, offset int) ([]Quest, error) {
	var quests []Quest
	err := r.db.Where("category_id = ? AND is_active = ?", categoryID, true).
		Limit(limit).Offset(offset).Find(&quests).Error
	return quests, err
}

func (r *repository) StartQuest(userQuest *UserQuest) error {
	return r.db.Create(userQuest).Error
}

func (r *repository) GetUserQuest(userID, questID uuid.UUID) (*UserQuest, error) {
	var userQuest UserQuest
	err := r.db.Preload("Quest").Preload("Progress").
		First(&userQuest, "user_id = ? AND quest_id = ?", userID, questID).Error
	if err != nil {
		return nil, err
	}
	return &userQuest, nil
}

func (r *repository) UpdateUserQuest(userQuest *UserQuest) error {
	return r.db.Save(userQuest).Error
}

func (r *repository) GetUserActiveQuests(userID uuid.UUID) ([]UserQuest, error) {
	var userQuests []UserQuest
	err := r.db.Preload("Quest").Where("user_id = ? AND status = ?", userID, QuestStatusInProgress).
		Find(&userQuests).Error
	return userQuests, err
}

func (r *repository) GetUserCompletedQuests(userID uuid.UUID, limit, offset int) ([]UserQuest, error) {
	var userQuests []UserQuest
	err := r.db.Preload("Quest").Where("user_id = ? AND status = ?", userID, QuestStatusCompleted).
		Limit(limit).Offset(offset).Find(&userQuests).Error
	return userQuests, err
}
