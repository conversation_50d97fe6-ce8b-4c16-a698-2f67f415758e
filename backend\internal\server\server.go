package server

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"quester-backend/internal/common/config"
	"quester-backend/internal/common/redis"
	"quester-backend/internal/domain/achievement"
	"quester-backend/internal/domain/marketplace"
	"quester-backend/internal/domain/notification"
	"quester-backend/internal/domain/quest"
	"quester-backend/internal/domain/user"
	"quester-backend/internal/middleware"

	"github.com/fasthttp/websocket"
	"github.com/gofiber/fiber/v3"
	"github.com/gofiber/fiber/v3/middleware/cors"
	"github.com/gofiber/fiber/v3/middleware/logger"
	"github.com/gofiber/fiber/v3/middleware/recover"
	"github.com/gofiber/fiber/v3/middleware/requestid"
	"github.com/valyala/fasthttp"
	"gorm.io/gorm"
)

type Server struct {
	app    *fiber.App
	config *config.Config
	db     *gorm.DB
	redis  *redis.Client
}

func New(cfg *config.Config, db *gorm.DB, redisClient *redis.Client) *Server {
	// Create Fiber app
	app := fiber.New(fiber.Config{
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
		ErrorHandler: middleware.ErrorHandler,
	})

	server := &Server{
		app:    app,
		config: cfg,
		db:     db,
		redis:  redisClient,
	}

	server.setupMiddleware()
	server.setupRoutes()

	return server
}

func (s *Server) setupMiddleware() {
	// Request ID middleware
	s.app.Use(requestid.New())

	// Logger middleware
	if s.config.Server.Environment == "development" {
		s.app.Use(logger.New(logger.Config{
			Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
		}))
	}

	// Recovery middleware
	s.app.Use(recover.New())

	// CORS middleware
	s.app.Use(cors.New(cors.Config{
		AllowOrigins:     strings.Split(s.config.CORS.AllowOrigins, ","),
		AllowMethods:     strings.Split(s.config.CORS.AllowMethods, ","),
		AllowHeaders:     strings.Split(s.config.CORS.AllowHeaders, ","),
		AllowCredentials: s.config.CORS.AllowCredentials,
		ExposeHeaders:    strings.Split(s.config.CORS.ExposeHeaders, ","),
		MaxAge:           s.config.CORS.MaxAge,
	}))

	// Custom middleware
	s.app.Use(middleware.SecurityHeaders())
	s.app.Use(middleware.RateLimit(s.redis))
}

func (s *Server) setupRoutes() {
	// Health check
	s.app.Get("/health", func(c fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":    "ok",
			"timestamp": time.Now().UTC(),
			"version":   "1.0.0",
		})
	})

	// API routes
	api := s.app.Group("/api/v1")

	// Initialize repositories
	userRepo := user.NewRepository(s.db)
	questRepo := quest.NewRepository(s.db)
	achievementRepo := achievement.NewRepository(s.db)
	marketplaceRepo := marketplace.NewRepository(s.db)
	notificationRepo := notification.NewRepository(s.db)

	// Initialize services
	userService := user.NewService(userRepo, s.redis, s.config.JWT.Secret)
	questService := quest.NewService(questRepo, userRepo, s.redis)
	achievementService := achievement.NewService(achievementRepo, userRepo, s.redis)
	marketplaceService := marketplace.NewService(marketplaceRepo, userRepo, s.redis)
	notificationService := notification.NewService(notificationRepo, s.redis)

	// Initialize handlers
	userHandler := user.NewHandler(userService)
	questHandler := quest.NewHandler(questService)
	achievementHandler := achievement.NewHandler(achievementService)
	marketplaceHandler := marketplace.NewHandler(marketplaceService)
	notificationHandler := notification.NewHandler(notificationService)

	// Auth routes (public)
	auth := api.Group("/auth")
	auth.Post("/register", userHandler.Register)
	auth.Post("/login", userHandler.Login)
	auth.Post("/refresh", userHandler.RefreshToken)
	auth.Post("/logout", userHandler.Logout)
	auth.Post("/forgot-password", userHandler.ForgotPassword)
	auth.Post("/reset-password", userHandler.ResetPassword)

	// Protected routes
	protected := api.Group("/", middleware.AuthRequired(s.config.JWT.Secret))

	// User routes
	users := protected.Group("/users")
	users.Get("/profile", userHandler.GetProfile)
	users.Put("/profile", userHandler.UpdateProfile)
	users.Get("/statistics", userHandler.GetStatistics)
	users.Get("/activities", userHandler.GetActivities)
	users.Get("/leaderboard", userHandler.GetLeaderboard)
	users.Get("/friends", userHandler.GetFriends)
	users.Post("/friends/:id", userHandler.AddFriend)
	users.Put("/friends/:id", userHandler.UpdateFriendship)
	users.Delete("/friends/:id", userHandler.RemoveFriend)
	users.Get("/wallet", userHandler.GetWallet)
	users.Get("/wallet/transactions", userHandler.GetWalletTransactions)

	// Quest routes
	quests := protected.Group("/quests")
	quests.Get("/", questHandler.GetQuests)
	quests.Get("/:id", questHandler.GetQuest)
	quests.Get("/categories", questHandler.GetCategories)
	quests.Get("/recommended", questHandler.GetRecommendedQuests)
	quests.Post("/:id/start", questHandler.StartQuest)
	quests.Put("/:id/progress", questHandler.UpdateProgress)
	quests.Post("/:id/complete", questHandler.CompleteQuest)
	quests.Post("/:id/claim", questHandler.ClaimReward)
	quests.Get("/user/active", questHandler.GetUserActiveQuests)
	quests.Get("/user/completed", questHandler.GetUserCompletedQuests)

	// Achievement routes
	achievements := protected.Group("/achievements")
	achievements.Get("/", achievementHandler.GetAchievements)
	achievements.Get("/:id", achievementHandler.GetAchievement)
	achievements.Get("/categories", achievementHandler.GetCategories)
	achievements.Get("/user", achievementHandler.GetUserAchievements)
	achievements.Post("/:id/claim", achievementHandler.ClaimReward)

	// Marketplace routes
	marketplace := protected.Group("/marketplace")
	marketplace.Get("/items", marketplaceHandler.GetItems)
	marketplace.Get("/items/:id", marketplaceHandler.GetItem)
	marketplace.Get("/categories", marketplaceHandler.GetCategories)
	marketplace.Get("/cart", marketplaceHandler.GetCart)
	marketplace.Post("/cart", marketplaceHandler.AddToCart)
	marketplace.Put("/cart/:id", marketplaceHandler.UpdateCartItem)
	marketplace.Delete("/cart/:id", marketplaceHandler.RemoveFromCart)
	marketplace.Post("/purchase", marketplaceHandler.PurchaseItem)
	marketplace.Post("/purchase/cart", marketplaceHandler.PurchaseCart)
	marketplace.Get("/inventory", marketplaceHandler.GetInventory)
	marketplace.Put("/inventory/:id/equip", marketplaceHandler.EquipItem)

	// Notification routes
	notifications := protected.Group("/notifications")
	notifications.Get("/", notificationHandler.GetNotifications)
	notifications.Get("/unread/count", notificationHandler.GetUnreadCount)
	notifications.Put("/:id/read", notificationHandler.MarkAsRead)
	notifications.Put("/read/all", notificationHandler.MarkAllAsRead)
	notifications.Delete("/:id", notificationHandler.DeleteNotification)

	// WebSocket endpoint
	s.app.Get("/ws", func(c fiber.Ctx) error {
		// Get the underlying fasthttp context
		fastCtx := c.RequestCtx()

		// Check if it's a WebSocket upgrade request
		if !websocket.FastHTTPIsWebSocketUpgrade(fastCtx) {
			return fiber.ErrUpgradeRequired
		}

		// Get user ID from query params before upgrading
		userID := string(fastCtx.QueryArgs().Peek("user_id"))

		// Upgrade to WebSocket
		upgrader := websocket.FastHTTPUpgrader{
			CheckOrigin: func(ctx *fasthttp.RequestCtx) bool {
				// Allow all origins for now (configure properly in production)
				return true
			},
		}

		return upgrader.Upgrade(fastCtx, func(conn *websocket.Conn) {
			s.handleWebSocket(conn, userID)
		})
	})

	// Admin routes (if user is admin)
	admin := protected.Group("/admin", middleware.AdminRequired())
	admin.Get("/users", userHandler.GetAllUsers)
	admin.Post("/quests", questHandler.CreateQuest)
	admin.Put("/quests/:id", questHandler.UpdateQuest)
	admin.Delete("/quests/:id", questHandler.DeleteQuest)
	admin.Post("/achievements", achievementHandler.CreateAchievement)
	admin.Put("/achievements/:id", achievementHandler.UpdateAchievement)
	admin.Delete("/achievements/:id", achievementHandler.DeleteAchievement)
	admin.Post("/marketplace/items", marketplaceHandler.CreateItem)
	admin.Put("/marketplace/items/:id", marketplaceHandler.UpdateItem)
	admin.Delete("/marketplace/items/:id", marketplaceHandler.DeleteItem)

	// Swagger documentation
	if s.config.Server.Environment == "development" {
		// TODO: Add Swagger setup
	}
}

func (s *Server) handleWebSocket(c *websocket.Conn, userID string) {
	// WebSocket connection handling
	defer c.Close()

	// Check if user ID is provided
	if userID == "" {
		log.Println("WebSocket connection without user_id")
		return
	}

	log.Printf("WebSocket connected for user: %s", userID)

	// Subscribe to user-specific notifications
	pubsub := s.redis.Subscribe(context.Background(), "notifications:"+userID)
	defer pubsub.Close()

	// Handle incoming messages
	for {
		select {
		case msg := <-pubsub.Channel():
			// Send notification to client
			if err := c.WriteMessage(websocket.TextMessage, []byte(msg.Payload)); err != nil {
				log.Printf("WebSocket write error: %v", err)
				return
			}
		default:
			// Read messages from client (for heartbeat, etc.)
			_, _, err := c.ReadMessage()
			if err != nil {
				log.Printf("WebSocket read error: %v", err)
				return
			}
		}
	}
}

func (s *Server) Start() error {
	address := fmt.Sprintf("%s:%s", s.config.Server.Host, s.config.Server.Port)
	log.Printf("Server starting on %s", address)
	return s.app.Listen(address)
}

func (s *Server) Shutdown() error {
	log.Println("Server shutting down...")
	return s.app.ShutdownWithTimeout(s.config.Server.ShutdownTimeout)
}
